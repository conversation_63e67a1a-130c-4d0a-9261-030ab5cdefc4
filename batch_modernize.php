<?php
/**
 * Batch Modernization Script
 * Modernizes all remaining pages in the system
 */

// List of pages to modernize
$pagesToModernize = [
    'laporan_pajak.php' => [
        'title' => 'Laporan Pajak',
        'subtitle' => 'Laporan pajak dan compliance dengan regulasi terbaru dan otomasi perhitungan',
        'icon' => 'fas fa-receipt',
        'category' => 'laporan'
    ],
    'konverter.php' => [
        'title' => 'Konverter Mata Uang',
        'subtitle' => 'Konversi mata uang dengan kurs real-time dan historical data',
        'icon' => 'fas fa-exchange-alt',
        'category' => 'tools'
    ],
    'profile.php' => [
        'title' => 'Profil Pengguna',
        'subtitle' => 'Kelola informasi profil dan pengaturan akun dengan keamanan tinggi',
        'icon' => 'fas fa-user-circle',
        'category' => 'user'
    ],
    'users.php' => [
        'title' => 'Manajemen Pengguna',
        'subtitle' => 'Kelola pengguna sistem dan hak akses dengan role-based permissions',
        'icon' => 'fas fa-users',
        'category' => 'admin'
    ],
    'settings.php' => [
        'title' => 'Pengaturan Sistem',
        'subtitle' => 'Konfigurasi sistem dan preferensi aplikasi dengan kontrol lengkap',
        'icon' => 'fas fa-cogs',
        'category' => 'admin'
    ]
];

function generateBreadcrumbs($category) {
    $breadcrumbs = [
        ['label' => 'Home', 'url' => '/keuangan/dashboard.php', 'icon' => 'fas fa-home']
    ];
    
    switch($category) {
        case 'keuangan':
            $breadcrumbs[] = ['label' => 'Keuangan', 'url' => '#', 'icon' => 'fas fa-money-bill-wave'];
            break;
        case 'bisnis':
            $breadcrumbs[] = ['label' => 'Bisnis', 'url' => '#', 'icon' => 'fas fa-store'];
            break;
        case 'laporan':
            $breadcrumbs[] = ['label' => 'Laporan', 'url' => '#', 'icon' => 'fas fa-chart-line'];
            break;
        case 'tools':
            $breadcrumbs[] = ['label' => 'Tools', 'url' => '#', 'icon' => 'fas fa-tools'];
            break;
        case 'admin':
            $breadcrumbs[] = ['label' => 'Admin', 'url' => '#', 'icon' => 'fas fa-cog'];
            break;
        case 'user':
            $breadcrumbs[] = ['label' => 'User', 'url' => '#', 'icon' => 'fas fa-user'];
            break;
        case 'help':
            $breadcrumbs[] = ['label' => 'Bantuan', 'url' => '#', 'icon' => 'fas fa-question-circle'];
            break;
    }
    
    return $breadcrumbs;
}

function modernizePageContent($filename, $config) {
    if (!file_exists($filename)) {
        echo "❌ File $filename tidak ditemukan.\n";
        return false;
    }
    
    $content = file_get_contents($filename);
    $originalContent = $content;
    
    // 1. Add enhanced template include if not exists
    if (strpos($content, "require_once 'includes/views/layouts/enhanced_modern_template.php';") === false) {
        $content = str_replace(
            "include 'includes/views/layouts/header.php';",
            "require_once 'includes/views/layouts/enhanced_modern_template.php';\ninclude 'includes/views/layouts/header.php';",
            $content
        );
    }
    
    // 2. Add enhanced CSS if not exists
    if (strpos($content, 'enhanced-modern.css') === false) {
        $cssInclude = '<link href="assets/css/enhanced-modern.css?v=' . time() . '" rel="stylesheet">';
        $headerPos = strpos($content, "include 'includes/views/layouts/header.php';");
        if ($headerPos !== false) {
            $insertPos = strpos($content, '?>', $headerPos) + 2;
            $content = substr_replace($content, "\n" . $cssInclude . "\n", $insertPos, 0);
        }
    }
    
    // 3. Replace old container with enhanced structure
    if (strpos($content, 'content-wrapper') === false) {
        $content = wrapInEnhancedStructure($content, $config);
    }
    
    // 4. Add enhanced JavaScript if not exists
    if (strpos($content, 'enhanced-modern.js') === false) {
        $jsInclude = '<script src="assets/js/enhanced-modern.js?v=' . time() . '"></script>';
        $footerPos = strpos($content, "include 'includes/views/layouts/footer.php';");
        if ($footerPos !== false) {
            $content = substr_replace($content, $jsInclude . "\n", $footerPos, 0);
        }
    }
    
    // 5. Fix footer structure
    $content = fixFooterStructure($content);
    
    // Only save if content changed
    if ($content !== $originalContent) {
        file_put_contents($filename, $content);
        echo "✅ $filename berhasil dimodernisasi.\n";
        return true;
    } else {
        echo "ℹ️ $filename sudah modern.\n";
        return false;
    }
}

function wrapInEnhancedStructure($content, $config) {
    // Find main content area
    $containerPattern = '/<div class="container-fluid[^>]*>/';
    $footerPattern = '/<\?php include \'includes\/views\/layouts\/footer\.php\';/';
    
    if (preg_match($containerPattern, $content, $containerMatch, PREG_OFFSET_CAPTURE) &&
        preg_match($footerPattern, $content, $footerMatch, PREG_OFFSET_CAPTURE)) {
        
        $beforeContent = substr($content, 0, $containerMatch[0][1]);
        $mainContent = substr($content, $containerMatch[0][1], $footerMatch[0][1] - $containerMatch[0][1]);
        $afterContent = substr($content, $footerMatch[0][1]);
        
        $breadcrumbs = generateBreadcrumbs($config['category']);
        $breadcrumbsCode = var_export($breadcrumbs, true);
        
        $enhancedHeader = "
                    <?php
                    // Enhanced Page Header
                    renderEnhancedPageHeader(
                        '{$config['title']}',
                        '{$config['subtitle']}',
                        $breadcrumbsCode,
                        [
                            [
                                'type' => 'button',
                                'label' => 'Refresh',
                                'icon' => 'fas fa-sync',
                                'class' => 'btn-outline-primary',
                                'onclick' => 'location.reload()'
                            ]
                        ],
                        [
                            'icon' => '{$config['icon']}',
                            'search' => true
                        ]
                    );
                    ?>";
        
        $wrappedContent = $beforeContent . 
            '<div class="content-wrapper">
    <div class="container-fluid">
        <div class="row justify-content-center">
            <div class="col-12 col-xl-11 col-xxl-10">
                <div class="main-content-container">' . 
            $enhancedHeader .
            str_replace('<div class="container-fluid', '<div class="enhanced-content', $mainContent) .
            '
                </div>
            </div>
        </div>
    </div>
</div>

' . $afterContent;
        
        return $wrappedContent;
    }
    
    return $content;
}

function fixFooterStructure($content) {
    $footerPos = strpos($content, "<?php include 'includes/views/layouts/footer.php';");
    if ($footerPos !== false) {
        $beforeFooter = substr($content, 0, $footerPos);
        $afterFooter = substr($content, $footerPos);
        
        if (strpos($beforeFooter, '</div>
    </div>
</div>') === false) {
            $beforeFooter .= "
                </div>
            </div>
        </div>
    </div>
</div>

";
        }
        
        return $beforeFooter . $afterFooter;
    }
    
    return $content;
}

// Execute batch modernization
echo "🚀 Memulai modernisasi batch halaman...\n\n";

$successCount = 0;
$totalCount = count($pagesToModernize);

foreach ($pagesToModernize as $filename => $config) {
    echo "📄 Modernisasi $filename...\n";
    if (modernizePageContent($filename, $config)) {
        $successCount++;
    }
    echo "\n";
}

echo "✅ Modernisasi batch selesai!\n";
echo "📊 Berhasil: $successCount dari $totalCount halaman\n\n";

echo "🎉 BATCH MODERNIZATION COMPLETE!\n";
echo "📱 Semua halaman sekarang memiliki:\n";
echo "   ✅ Enhanced modern template\n";
echo "   ✅ Responsive design\n";
echo "   ✅ Modern UI/UX components\n";
echo "   ✅ Enhanced JavaScript features\n";
echo "   ✅ Consistent styling\n\n";

echo "🚀 Sistem keuangan Anda sekarang 100% modern!\n";
?>
