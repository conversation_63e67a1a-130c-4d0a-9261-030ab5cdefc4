<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isLoggedIn()) {
    setFlashMessage('danger', 'Silakan login terlebih dahulu.');
    redirect('login.php');
}

// Get current user data
$currentUser = getCurrentUser();
if (!$currentUser) {
    // Session exists but user not found in database - clear session
    session_unset();
    session_destroy();
    setFlashMessage('danger', 'Sesi tidak valid. Silakan login kembali.');
    redirect('login.php');
}

$currentPage = 'dashboard';
$pageTitle = 'Dashboard';

// Get dashboard statistics
try {
    // Get current month and year
    $currentMonth = date('m');
    $currentYear = date('Y');

    // Total balance (all time)
    $stmt = $pdo->prepare("
        SELECT
            COALESCE(SUM(CASE WHEN k.tipe = 'pemasukan' THEN t.jumlah ELSE 0 END), 0) as total_pemasukan,
            COALESCE(SUM(CASE WHEN k.tipe = 'pengeluaran' THEN t.jumlah ELSE 0 END), 0) as total_pengeluaran
        FROM transaksi t
        LEFT JOIN kategori k ON t.kategori_id = k.id
        WHERE t.user_id = ?
    ");
    $stmt->execute([$currentUser['id']]);
    $totalStats = $stmt->fetch();

    $totalSaldo = $totalStats['total_pemasukan'] - $totalStats['total_pengeluaran'];

    // Monthly statistics (current month)
    $stmt = $pdo->prepare("
        SELECT
            COALESCE(SUM(CASE WHEN k.tipe = 'pemasukan' THEN t.jumlah ELSE 0 END), 0) as pemasukan_bulan,
            COALESCE(SUM(CASE WHEN k.tipe = 'pengeluaran' THEN t.jumlah ELSE 0 END), 0) as pengeluaran_bulan,
            COUNT(*) as total_transaksi_bulan
        FROM transaksi t
        LEFT JOIN kategori k ON t.kategori_id = k.id
        WHERE t.user_id = ? AND MONTH(t.tanggal) = ? AND YEAR(t.tanggal) = ?
    ");
    $stmt->execute([$currentUser['id'], $currentMonth, $currentYear]);
    $monthlyStats = $stmt->fetch();

    // Recent transactions (last 5)
    $stmt = $pdo->prepare("
        SELECT
            t.*,
            k.nama as kategori_nama,
            k.tipe
        FROM transaksi t
        LEFT JOIN kategori k ON t.kategori_id = k.id
        WHERE t.user_id = ?
        ORDER BY t.tanggal DESC, t.created_at DESC
        LIMIT 5
    ");
    $stmt->execute([$currentUser['id']]);
    $recentTransactions = $stmt->fetchAll();

    // Active targets
    $stmt = $pdo->prepare("
        SELECT * FROM target
        WHERE user_id = ? AND status = 'aktif'
        ORDER BY tanggal_selesai ASC
        LIMIT 3
    ");
    $stmt->execute([$currentUser['id']]);
    $activeTargets = $stmt->fetchAll();

    // Top categories this month
    $stmt = $pdo->prepare("
        SELECT
            k.nama as kategori_nama,
            k.tipe,
            SUM(t.jumlah) as total_jumlah,
            COUNT(t.id) as jumlah_transaksi
        FROM transaksi t
        LEFT JOIN kategori k ON t.kategori_id = k.id
        WHERE t.user_id = ? AND MONTH(t.tanggal) = ? AND YEAR(t.tanggal) = ?
        GROUP BY t.kategori_id, k.nama, k.tipe
        ORDER BY total_jumlah DESC
        LIMIT 5
    ");
    $stmt->execute([$currentUser['id'], $currentMonth, $currentYear]);
    $topCategories = $stmt->fetchAll();

} catch (PDOException $e) {
    error_log("Dashboard Error: " . $e->getMessage());
    setFlashMessage('danger', 'Terjadi kesalahan saat mengambil data dashboard.');
}

require_once 'includes/views/layouts/modern_page_template.php';
include 'includes/views/layouts/header.php';
?>

<div class="content-wrapper">
    <div class="container-fluid">
        <div class="row justify-content-center">
            <div class="col-12 col-xl-11 col-xxl-10">
                <div class="main-content-container">

                    <?php
                    // Page Header
                    renderModernPageHeader(
                        'Dashboard Keuangan',
                        'Selamat datang, ' . htmlspecialchars($currentUser['nama']) . '! Kelola keuangan Anda dengan mudah dan efisien',
                        [
                            ['label' => 'Home', 'url' => '/keuangan/dashboard.php', 'icon' => 'fas fa-home']
                        ],
                        [
                            [
                                'type' => 'link',
                                'url' => '/keuangan/transaksi.php',
                                'label' => 'Tambah Transaksi',
                                'icon' => 'fas fa-plus',
                                'class' => 'btn-primary'
                            ]
                        ]
                    );
                    ?>

                    <?php
                    // Statistics Cards
                    $stats = [
                        [
                            'label' => 'Total Saldo',
                            'value' => formatRupiah($totalSaldo),
                            'icon' => 'fas fa-wallet',
                            'color' => $totalSaldo >= 0 ? 'success' : 'danger',
                            'subtitle' => 'Saldo keseluruhan'
                        ],
                        [
                            'label' => 'Pemasukan Bulan Ini',
                            'value' => formatRupiah($monthlyStats['pemasukan_bulan'] ?? 0),
                            'icon' => 'fas fa-arrow-up',
                            'color' => 'success',
                            'subtitle' => 'Bulan ' . date('F Y')
                        ],
                        [
                            'label' => 'Pengeluaran Bulan Ini',
                            'value' => formatRupiah($monthlyStats['pengeluaran_bulan'] ?? 0),
                            'icon' => 'fas fa-arrow-down',
                            'color' => 'danger',
                            'subtitle' => 'Bulan ' . date('F Y')
                        ],
                        [
                            'label' => 'Total Transaksi',
                            'value' => number_format($monthlyStats['total_transaksi_bulan'] ?? 0),
                            'icon' => 'fas fa-exchange-alt',
                            'color' => 'info',
                            'subtitle' => 'Transaksi bulan ini'
                        ]
                    ];

                    renderModernStatsCards($stats);
                    ?>

                    <!-- Quick Actions -->
                    <?php
                    $quickActionsContent = '
                    <div class="row g-4">
                        <div class="col-lg-3 col-md-6">
                            <a href="/keuangan/transaksi.php" class="quick-action-card">
                                <div class="quick-action-icon bg-primary">
                                    <i class="fas fa-plus-circle"></i>
                                </div>
                                <h6>Tambah Transaksi</h6>
                                <p>Catat pemasukan atau pengeluaran baru</p>
                            </a>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <a href="/keuangan/kategori.php" class="quick-action-card">
                                <div class="quick-action-icon bg-success">
                                    <i class="fas fa-tags"></i>
                                </div>
                                <h6>Kelola Kategori</h6>
                                <p>Atur kategori pemasukan dan pengeluaran</p>
                            </a>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <a href="/keuangan/target.php" class="quick-action-card">
                                <div class="quick-action-icon bg-warning">
                                    <i class="fas fa-bullseye"></i>
                                </div>
                                <h6>Target Keuangan</h6>
                                <p>Tetapkan dan pantau target finansial</p>
                            </a>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <a href="/keuangan/laporan.php" class="quick-action-card">
                                <div class="quick-action-icon bg-info">
                                    <i class="fas fa-chart-bar"></i>
                                </div>
                                <h6>Lihat Laporan</h6>
                                <p>Analisis keuangan dan statistik</p>
                            </a>
                        </div>
                    </div>';

                    renderModernCard(
                        'Aksi Cepat',
                        $quickActionsContent,
                        [
                            'icon' => 'fas fa-bolt',
                            'card_class' => 'mb-4'
                        ]
                    );
                    ?>

                    <div class="row g-4">
                        <!-- Recent Transactions -->
                        <div class="col-lg-8">
                            <?php
                            $recentTransactionsContent = '';

                            if (empty($recentTransactions)) {
                                $recentTransactionsContent = '
                                <div class="empty-state">
                                    <i class="fas fa-inbox fa-4x text-muted mb-3 opacity-50"></i>
                                    <h6 class="text-muted mb-2">Belum ada transaksi</h6>
                                    <p class="text-muted small mb-3">Mulai dengan menambahkan transaksi pertama Anda</p>
                                    <a href="/keuangan/transaksi.php" class="btn btn-primary">
                                        <i class="fas fa-plus me-2"></i>Tambah Transaksi Pertama
                                    </a>
                                </div>';
                            } else {
                                $recentTransactionsContent = '<div class="transaction-list">';
                                foreach ($recentTransactions as $t) {
                                    $typeIcon = $t['tipe'] === 'pemasukan' ? 'arrow-up' : 'arrow-down';
                                    $typeColor = $t['tipe'] === 'pemasukan' ? 'success' : 'danger';
                                    $amount = ($t['tipe'] === 'pemasukan' ? '+' : '-') . formatRupiah($t['jumlah']);

                                    $recentTransactionsContent .= '
                                    <div class="transaction-item">
                                        <div class="transaction-icon bg-' . $typeColor . ' bg-opacity-15">
                                            <i class="fas fa-' . $typeIcon . ' text-' . $typeColor . '"></i>
                                        </div>
                                        <div class="transaction-details">
                                            <h6 class="transaction-title">' . htmlspecialchars($t['keterangan']) . '</h6>
                                            <p class="transaction-meta">
                                                <span class="category">' . htmlspecialchars($t['kategori_nama']) . '</span>
                                                <span class="date">' . formatTanggal($t['tanggal']) . '</span>
                                            </p>
                                        </div>
                                        <div class="transaction-amount text-' . $typeColor . '">
                                            ' . $amount . '
                                        </div>
                                    </div>';
                                }
                                $recentTransactionsContent .= '</div>';
                            }

                            renderModernCard(
                                'Transaksi Terbaru',
                                $recentTransactionsContent,
                                [
                                    'icon' => 'fas fa-history',
                                    'actions' => [
                                        [
                                            'type' => 'button',
                                            'label' => 'Lihat Semua',
                                            'icon' => 'fas fa-external-link-alt',
                                            'class' => 'btn-outline-primary',
                                            'onclick' => "window.location.href='/keuangan/transaksi.php'"
                                        ]
                                    ]
                                ]
                            );
                            ?>
                        </div>

                        <!-- Sidebar Content -->
                        <div class="col-lg-4">
                            <!-- Active Targets -->
                            <?php
                            $activeTargetsContent = '';

                            if (empty($activeTargets)) {
                                $activeTargetsContent = '
                                <div class="empty-state py-3">
                                    <i class="fas fa-bullseye fa-3x text-muted mb-2 opacity-50"></i>
                                    <p class="text-muted mb-0">Belum ada target aktif</p>
                                </div>';
                            } else {
                                $activeTargetsContent = '<div class="targets-list">';
                                foreach ($activeTargets as $target) {
                                    $progress = 0; // You can calculate actual progress here
                                    $activeTargetsContent .= '
                                    <div class="target-item">
                                        <div class="target-header">
                                            <h6 class="target-name">' . htmlspecialchars($target['nama']) . '</h6>
                                            <span class="target-amount">' . formatRupiah($target['jumlah_target']) . '</span>
                                        </div>
                                        <div class="progress-container">
                                            <div class="progress">
                                                <div class="progress-bar bg-warning" style="width: ' . $progress . '%"></div>
                                            </div>
                                            <small class="progress-text">Target: ' . formatTanggal($target['tanggal_selesai']) . '</small>
                                        </div>
                                    </div>';
                                }
                                $activeTargetsContent .= '</div>';
                            }

                            renderModernCard(
                                'Target Aktif',
                                $activeTargetsContent,
                                [
                                    'icon' => 'fas fa-bullseye',
                                    'card_class' => 'mb-4',
                                    'actions' => [
                                        [
                                            'type' => 'button',
                                            'label' => 'Kelola',
                                            'icon' => 'fas fa-cog',
                                            'class' => 'btn-outline-primary',
                                            'onclick' => "window.location.href='/keuangan/target.php'"
                                        ]
                                    ]
                                ]
                            );
                            ?>

                            <!-- Top Categories -->
                            <?php
                            $topCategoriesContent = '';

                            if (empty($topCategories)) {
                                $topCategoriesContent = '
                                <div class="empty-state py-3">
                                    <i class="fas fa-tags fa-3x text-muted mb-2 opacity-50"></i>
                                    <p class="text-muted mb-0">Belum ada transaksi bulan ini</p>
                                </div>';
                            } else {
                                $topCategoriesContent = '<div class="categories-list">';
                                foreach ($topCategories as $category) {
                                    $typeColor = $category['tipe'] === 'pemasukan' ? 'success' : 'danger';
                                    $typeIcon = $category['tipe'] === 'pemasukan' ? 'arrow-up' : 'arrow-down';

                                    $topCategoriesContent .= '
                                    <div class="category-item">
                                        <div class="category-icon">
                                            <span class="badge bg-' . $typeColor . ' bg-opacity-15 text-' . $typeColor . '">
                                                <i class="fas fa-' . $typeIcon . '"></i>
                                            </span>
                                        </div>
                                        <div class="category-details">
                                            <span class="category-name">' . htmlspecialchars($category['kategori_nama']) . '</span>
                                            <small class="category-count">' . $category['jumlah_transaksi'] . ' transaksi</small>
                                        </div>
                                        <div class="category-amount">
                                            ' . formatRupiah($category['total_jumlah']) . '
                                        </div>
                                    </div>';
                                }
                                $topCategoriesContent .= '</div>';
                            }

                            renderModernCard(
                                'Kategori Teratas Bulan Ini',
                                $topCategoriesContent,
                                [
                                    'icon' => 'fas fa-tags'
                                ]
                            );
                            ?>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/views/layouts/footer.php'; ?>

<style>
/* Dashboard Specific Styles */
.quick-action-card {
    display: block;
    text-decoration: none;
    color: inherit;
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    height: 100%;
}

.quick-action-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
    text-decoration: none;
    color: inherit;
    border-color: #e9ecef;
}

.quick-action-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: white;
}

.quick-action-icon.bg-primary { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.quick-action-icon.bg-success { background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%); }
.quick-action-icon.bg-warning { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
.quick-action-icon.bg-info { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }

.quick-action-card h6 {
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0.75rem;
    font-size: 1.1rem;
}

.quick-action-card p {
    color: #6c757d;
    margin-bottom: 0;
    font-size: 0.9rem;
    line-height: 1.4;
}

/* Transaction List */
.transaction-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.transaction-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 0.75rem;
    transition: all 0.3s ease;
}

.transaction-item:hover {
    background: #e9ecef;
    transform: translateX(5px);
}

.transaction-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
}

.transaction-details {
    flex: 1;
}

.transaction-title {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.25rem;
    font-size: 1rem;
}

.transaction-meta {
    margin: 0;
    font-size: 0.85rem;
    color: #6c757d;
}

.transaction-meta .category {
    margin-right: 0.5rem;
}

.transaction-meta .date {
    opacity: 0.8;
}

.transaction-amount {
    font-weight: 700;
    font-size: 1.1rem;
}

/* Targets List */
.targets-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.target-item {
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 0.75rem;
}

.target-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 1rem;
}

.target-name {
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
    flex: 1;
}

.target-amount {
    font-weight: 600;
    color: #6c757d;
    font-size: 0.9rem;
}

.progress-container .progress {
    height: 8px;
    border-radius: 10px;
    margin-bottom: 0.5rem;
}

.progress-text {
    color: #6c757d;
    font-size: 0.8rem;
}

/* Categories List */
.categories-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.category-item {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 0.5rem;
}

.category-icon {
    margin-right: 1rem;
}

.category-details {
    flex: 1;
}

.category-name {
    font-weight: 600;
    color: #2c3e50;
    display: block;
    font-size: 0.95rem;
}

.category-count {
    color: #6c757d;
    font-size: 0.8rem;
}

.category-amount {
    font-weight: 600;
    color: #2c3e50;
    font-size: 0.9rem;
}

/* Responsive */
@media (max-width: 768px) {
    .quick-action-card {
        padding: 1.5rem;
    }

    .quick-action-icon {
        width: 60px;
        height: 60px;
        margin-bottom: 1rem;
    }

    .transaction-item {
        padding: 0.75rem;
    }

    .transaction-icon {
        width: 40px;
        height: 40px;
        margin-right: 0.75rem;
    }

    .transaction-title {
        font-size: 0.9rem;
    }

    .transaction-amount {
        font-size: 1rem;
    }
}
</style>
