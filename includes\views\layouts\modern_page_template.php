<?php
/**
 * Modern Page Template
 * Template konsisten untuk semua halaman dengan UI/UX modern
 */

function renderModernPageHeader($title, $subtitle = '', $breadcrumbs = [], $actions = []) {
    ?>
    <div class="modern-page-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <div class="page-title-section">
                    <?php if (!empty($breadcrumbs)): ?>
                    <nav aria-label="breadcrumb" class="mb-2">
                        <ol class="breadcrumb modern-breadcrumb">
                            <?php foreach ($breadcrumbs as $index => $crumb): ?>
                                <?php if ($index === count($breadcrumbs) - 1): ?>
                                    <li class="breadcrumb-item active" aria-current="page">
                                        <i class="<?= $crumb['icon'] ?? 'fas fa-circle' ?> me-1"></i>
                                        <?= htmlspecialchars($crumb['label']) ?>
                                    </li>
                                <?php else: ?>
                                    <li class="breadcrumb-item">
                                        <a href="<?= $crumb['url'] ?? '#' ?>" class="breadcrumb-link">
                                            <i class="<?= $crumb['icon'] ?? 'fas fa-home' ?> me-1"></i>
                                            <?= htmlspecialchars($crumb['label']) ?>
                                        </a>
                                    </li>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </ol>
                    </nav>
                    <?php endif; ?>
                    
                    <h1 class="page-title"><?= htmlspecialchars($title) ?></h1>
                    <?php if ($subtitle): ?>
                        <p class="page-subtitle"><?= htmlspecialchars($subtitle) ?></p>
                    <?php endif; ?>
                </div>
            </div>
            
            <?php if (!empty($actions)): ?>
            <div class="col-md-4 text-md-end mt-3 mt-md-0">
                <div class="page-actions">
                    <?php foreach ($actions as $action): ?>
                        <?php if ($action['type'] === 'button'): ?>
                            <button type="button" 
                                    class="btn <?= $action['class'] ?? 'btn-primary' ?> <?= $action['size'] ?? '' ?>"
                                    <?= isset($action['onclick']) ? 'onclick="' . $action['onclick'] . '"' : '' ?>
                                    <?= isset($action['data-bs-toggle']) ? 'data-bs-toggle="' . $action['data-bs-toggle'] . '"' : '' ?>
                                    <?= isset($action['data-bs-target']) ? 'data-bs-target="' . $action['data-bs-target'] . '"' : '' ?>>
                                <i class="<?= $action['icon'] ?? 'fas fa-plus' ?> me-2"></i>
                                <?= htmlspecialchars($action['label']) ?>
                            </button>
                        <?php elseif ($action['type'] === 'link'): ?>
                            <a href="<?= $action['url'] ?>" 
                               class="btn <?= $action['class'] ?? 'btn-primary' ?> <?= $action['size'] ?? '' ?>">
                                <i class="<?= $action['icon'] ?? 'fas fa-link' ?> me-2"></i>
                                <?= htmlspecialchars($action['label']) ?>
                            </a>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
    <?php
}

function renderModernStatsCards($stats) {
    ?>
    <div class="modern-stats-section">
        <div class="row g-4">
            <?php foreach ($stats as $index => $stat): ?>
            <div class="col-lg-3 col-md-6">
                <div class="modern-stats-card stats-card-<?= $index + 1 ?>">
                    <div class="stats-card-body">
                        <div class="stats-content">
                            <div class="stats-info">
                                <h6 class="stats-label"><?= htmlspecialchars($stat['label']) ?></h6>
                                <h2 class="stats-value"><?= $stat['value'] ?></h2>
                                <?php if (isset($stat['subtitle'])): ?>
                                    <small class="stats-subtitle"><?= htmlspecialchars($stat['subtitle']) ?></small>
                                <?php endif; ?>
                                <?php if (isset($stat['change'])): ?>
                                    <div class="stats-change <?= $stat['change']['type'] ?>">
                                        <i class="fas fa-<?= $stat['change']['type'] === 'positive' ? 'arrow-up' : 'arrow-down' ?> me-1"></i>
                                        <?= $stat['change']['value'] ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="stats-icon">
                                <div class="icon-wrapper <?= $stat['color'] ?? 'primary' ?>">
                                    <i class="<?= $stat['icon'] ?? 'fas fa-chart-bar' ?>"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
    <?php
}

function renderModernCard($title, $content, $options = []) {
    $headerClass = $options['header_class'] ?? 'bg-white';
    $bodyClass = $options['body_class'] ?? '';
    $cardClass = $options['card_class'] ?? '';
    $icon = $options['icon'] ?? '';
    $actions = $options['actions'] ?? [];
    ?>
    <div class="modern-card <?= $cardClass ?>">
        <div class="card-header <?= $headerClass ?>">
            <div class="row align-items-center">
                <div class="col">
                    <h5 class="card-title">
                        <?php if ($icon): ?>
                            <i class="<?= $icon ?> text-primary me-2"></i>
                        <?php endif; ?>
                        <?= htmlspecialchars($title) ?>
                    </h5>
                </div>
                <?php if (!empty($actions)): ?>
                <div class="col-auto">
                    <div class="card-actions">
                        <?php foreach ($actions as $action): ?>
                            <?php if ($action['type'] === 'button'): ?>
                                <button type="button" 
                                        class="btn <?= $action['class'] ?? 'btn-outline-secondary' ?> btn-sm"
                                        <?= isset($action['onclick']) ? 'onclick="' . $action['onclick'] . '"' : '' ?>>
                                    <i class="<?= $action['icon'] ?? 'fas fa-cog' ?>"></i>
                                    <?php if (isset($action['label'])): ?>
                                        <span class="d-none d-md-inline ms-1"><?= $action['label'] ?></span>
                                    <?php endif; ?>
                                </button>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
        <div class="card-body <?= $bodyClass ?>">
            <?= $content ?>
        </div>
    </div>
    <?php
}

function renderModernTable($headers, $data, $options = []) {
    $tableClass = $options['table_class'] ?? 'table-hover';
    $emptyMessage = $options['empty_message'] ?? 'Tidak ada data untuk ditampilkan';
    $emptyIcon = $options['empty_icon'] ?? 'fas fa-inbox';
    $actions = $options['actions'] ?? [];

    ob_start();
    ?>
    <div class="modern-table-container">
        <div class="table-responsive">
            <table class="table modern-table <?= $tableClass ?>">
                <thead class="table-header">
                    <tr>
                        <?php foreach ($headers as $header): ?>
                            <th class="<?= $header['class'] ?? '' ?>"><?= htmlspecialchars($header['label']) ?></th>
                        <?php endforeach; ?>
                        <?php if (!empty($actions)): ?>
                            <th class="text-center">Aksi</th>
                        <?php endif; ?>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($data)): ?>
                        <tr>
                            <td colspan="<?= count($headers) + (!empty($actions) ? 1 : 0) ?>" class="text-center py-5">
                                <div class="empty-state">
                                    <i class="<?= $emptyIcon ?> fa-3x text-muted mb-3 opacity-50"></i>
                                    <h6 class="text-muted"><?= htmlspecialchars($emptyMessage) ?></h6>
                                </div>
                            </td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($data as $row): ?>
                            <tr class="table-row">
                                <?php foreach ($headers as $header): ?>
                                    <td class="<?= $header['class'] ?? '' ?>">
                                        <?= $row[$header['key']] ?? '' ?>
                                    </td>
                                <?php endforeach; ?>
                                <?php if (!empty($actions)): ?>
                                    <td class="text-center">
                                        <div class="btn-group btn-group-sm">
                                            <?php foreach ($actions as $action): ?>
                                                <button type="button"
                                                        class="btn <?= $action['class'] ?? 'btn-outline-secondary' ?>"
                                                        onclick="<?= str_replace('{id}', $row['id'] ?? '', $action['onclick']) ?>"
                                                        title="<?= $action['title'] ?? '' ?>">
                                                    <i class="<?= $action['icon'] ?? 'fas fa-cog' ?>"></i>
                                                </button>
                                            <?php endforeach; ?>
                                        </div>
                                    </td>
                                <?php endif; ?>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
    <?php
    return ob_get_clean();
}

function renderModernFilter($filters, $currentFilters = []) {
    ?>
    <div class="modern-filter-section">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-light">
                <h6 class="mb-0 fw-semibold">
                    <i class="fas fa-filter text-primary me-2"></i>Filter & Pencarian
                </h6>
            </div>
            <div class="card-body">
                <form method="GET" class="filter-form">
                    <div class="row g-3">
                        <?php foreach ($filters as $filter): ?>
                            <div class="col-md-<?= $filter['col'] ?? '3' ?>">
                                <label class="form-label fw-semibold"><?= htmlspecialchars($filter['label']) ?></label>
                                <?php if ($filter['type'] === 'text'): ?>
                                    <div class="input-group">
                                        <span class="input-group-text bg-light">
                                            <i class="<?= $filter['icon'] ?? 'fas fa-search' ?> text-muted"></i>
                                        </span>
                                        <input type="text" 
                                               name="<?= $filter['name'] ?>" 
                                               class="form-control border-start-0 ps-0"
                                               placeholder="<?= $filter['placeholder'] ?? '' ?>"
                                               value="<?= htmlspecialchars($currentFilters[$filter['name']] ?? '') ?>">
                                    </div>
                                <?php elseif ($filter['type'] === 'select'): ?>
                                    <select name="<?= $filter['name'] ?>" class="form-select">
                                        <option value=""><?= $filter['placeholder'] ?? 'Semua' ?></option>
                                        <?php foreach ($filter['options'] as $value => $label): ?>
                                            <option value="<?= $value ?>" 
                                                    <?= ($currentFilters[$filter['name']] ?? '') === $value ? 'selected' : '' ?>>
                                                <?= htmlspecialchars($label) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; ?>
                        <div class="col-md-2 d-flex align-items-end">
                            <div class="btn-group w-100">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-2"></i>Cari
                                </button>
                                <a href="<?= $_SERVER['PHP_SELF'] ?>" class="btn btn-outline-secondary" title="Reset">
                                    <i class="fas fa-times"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <?php
}
?>
