<?php
// <PERSON><PERSON> session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include file yang diperlukan
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';

// Cek apakah user sudah login
if (!isLoggedIn()) {
    setFlashMessage('danger', 'Silakan login terlebih dahulu');
    redirect('login.php');
}

// Dapatkan data user yang sedang login
$currentUser = getCurrentUser();
if (!$currentUser) {
    session_destroy();
    setFlashMessage('danger', 'Sesi tidak valid. Silakan login kembali.');
    redirect('login.php');
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        try {
            switch ($_POST['action']) {
                case 'add':
                    $stmt = $pdo->prepare("
                        INSERT INTO target (user_id, nama, jumlah_target, tanggal_mulai, tanggal_selesai)
                        VALUES (?, ?, ?, ?, ?)
                    ");
                    $stmt->execute([
                        $currentUser['id'],
                        $_POST['nama'],
                        $_POST['jumlah_target'],
                        $_POST['tanggal_mulai'],
                        $_POST['tanggal_selesai']
                    ]);
                    setFlashMessage('success', 'Target berhasil ditambahkan');
                    break;

                case 'update_status':
                    $stmt = $pdo->prepare("
                        UPDATE target 
                        SET status = ? 
                        WHERE id = ? AND user_id = ?
                    ");
                    $stmt->execute([$_POST['status'], $_POST['id'], $currentUser['id']]);
                    setFlashMessage('success', 'Status target berhasil diperbarui');
                    break;

                case 'update_progress':
                    $stmt = $pdo->prepare("
                        UPDATE target 
                        SET jumlah_terkumpul = ? 
                        WHERE id = ? AND user_id = ?
                    ");
                    $stmt->execute([$_POST['jumlah_terkumpul'], $_POST['id'], $currentUser['id']]);
                    setFlashMessage('success', 'Progress target berhasil diperbarui');
                    break;

                case 'delete':
                    $stmt = $pdo->prepare("DELETE FROM target WHERE id = ? AND user_id = ?");
                    $stmt->execute([$_POST['id'], $currentUser['id']]);
                    setFlashMessage('success', 'Target berhasil dihapus');
                    break;
            }
        } catch (PDOException $e) {
            error_log("Target Error: " . $e->getMessage());
            setFlashMessage('danger', 'Terjadi kesalahan. Silakan coba lagi.');
        }
        redirect('target.php');
    }
}

// Get all targets
try {
    $stmt = $pdo->prepare("
        SELECT * FROM target 
        WHERE user_id = ?
        ORDER BY 
            CASE 
                WHEN status = 'aktif' THEN 1
                WHEN status = 'selesai' THEN 2
                ELSE 3
            END,
            tanggal_selesai ASC
    ");
    $stmt->execute([$currentUser['id']]);
    $targets = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    error_log("Target Error: " . $e->getMessage());
    setFlashMessage('danger', 'Terjadi kesalahan saat mengambil data target.');
    $targets = [];
}

// Set page title
$page_title = 'Target Keuangan';

// Include header
require_once 'includes/views/layouts/modern_page_template.php';
include 'includes/views/layouts/header.php';
?>

<div class="content-wrapper">
    <div class="container-fluid">
        <div class="row justify-content-center">
            <div class="col-12 col-xl-11 col-xxl-10">
                <div class="main-content-container">

                    <?php
                    // Page Header
                    renderModernPageHeader(
                        'Target Keuangan',
                        'Tetapkan dan pantau target keuangan Anda untuk mencapai tujuan finansial',
                        [
                            ['label' => 'Home', 'url' => '/keuangan/dashboard.php', 'icon' => 'fas fa-home'],
                            ['label' => 'Target Keuangan', 'url' => '/keuangan/target.php', 'icon' => 'fas fa-bullseye']
                        ],
                        [
                            [
                                'type' => 'button',
                                'label' => 'Tambah Target',
                                'icon' => 'fas fa-plus',
                                'class' => 'btn-primary',
                                'data-bs-toggle' => 'modal',
                                'data-bs-target' => '#addTargetModal'
                            ]
                        ]
                    );
                    ?>

                    <!-- Statistics Cards -->
                    <?php
                    $totalTargets = count($targets);
                    $activeTargets = count(array_filter($targets, function($t) { return $t['status'] === 'aktif'; }));
                    $completedTargets = count(array_filter($targets, function($t) { return $t['status'] === 'selesai'; }));
                    $totalTargetAmount = array_sum(array_column($targets, 'jumlah_target'));
                    $totalCollected = array_sum(array_column($targets, 'jumlah_terkumpul'));

                    $stats = [
                        [
                            'label' => 'Total Target',
                            'value' => number_format($totalTargets),
                            'icon' => 'fas fa-bullseye',
                            'color' => 'primary',
                            'subtitle' => 'Target yang dibuat'
                        ],
                        [
                            'label' => 'Target Aktif',
                            'value' => number_format($activeTargets),
                            'icon' => 'fas fa-play-circle',
                            'color' => 'success',
                            'subtitle' => 'Sedang berjalan'
                        ],
                        [
                            'label' => 'Target Selesai',
                            'value' => number_format($completedTargets),
                            'icon' => 'fas fa-check-circle',
                            'color' => 'info',
                            'subtitle' => 'Berhasil dicapai'
                        ],
                        [
                            'label' => 'Total Terkumpul',
                            'value' => formatRupiah($totalCollected),
                            'icon' => 'fas fa-coins',
                            'color' => 'warning',
                            'subtitle' => 'Dari ' . formatRupiah($totalTargetAmount)
                        ]
                    ];

                    renderModernStatsCards($stats);
                    ?>

                    <!-- Targets Grid -->
                    <div class="row g-4">
                        <?php if (empty($targets)): ?>
                        <div class="col-12">
                            <div class="empty-state text-center py-5">
                                <i class="fas fa-bullseye fa-4x text-muted mb-3 opacity-50"></i>
                                <h5 class="text-muted mb-2">Belum ada target keuangan</h5>
                                <p class="text-muted mb-3">Mulai dengan menetapkan target keuangan pertama Anda</p>
                                <button type="button" class="btn btn-primary btn-lg" data-bs-toggle="modal" data-bs-target="#addTargetModal">
                                    <i class="fas fa-plus me-2"></i>Tambah Target Pertama
                                </button>
                            </div>
                        </div>
                        <?php else: ?>
                            <?php foreach ($targets as $target):
                                $progress = $target['jumlah_target'] > 0 ? ($target['jumlah_terkumpul'] / $target['jumlah_target']) * 100 : 0;
                                $progress = min($progress, 100);
                                $status_colors = [
                                    'aktif' => 'success',
                                    'selesai' => 'primary',
                                    'batal' => 'danger'
                                ];
                                $status_color = $status_colors[$target['status']] ?? 'secondary';

                                // Calculate days remaining
                                $today = new DateTime();
                                $endDate = new DateTime($target['tanggal_selesai']);
                                $daysRemaining = $today->diff($endDate)->days;
                                $isOverdue = $today > $endDate;
                            ?>
                            <div class="col-lg-4 col-md-6">
                                <div class="modern-target-card">
                                    <div class="target-card-header">
                                        <div class="target-info">
                                            <h5 class="target-title"><?= htmlspecialchars($target['nama']) ?></h5>
                                            <span class="target-status badge bg-<?= $status_color ?> bg-opacity-15 text-<?= $status_color ?>">
                                                <i class="fas fa-<?= $target['status'] === 'aktif' ? 'play' : ($target['status'] === 'selesai' ? 'check' : 'times') ?> me-1"></i>
                                                <?= ucfirst($target['status']) ?>
                                            </span>
                                        </div>
                                    </div>

                                    <div class="target-progress-section">
                                        <div class="progress-info">
                                            <div class="progress-labels">
                                                <span class="progress-label">Target</span>
                                                <span class="progress-amount"><?= formatRupiah($target['jumlah_target']) ?></span>
                                            </div>
                                            <div class="progress-bar-container">
                                                <div class="progress modern-progress">
                                                    <div class="progress-bar bg-<?= $progress >= 100 ? 'success' : 'primary' ?>"
                                                         style="width: <?= $progress ?>%">
                                                    </div>
                                                </div>
                                                <span class="progress-percentage"><?= number_format($progress, 1) ?>%</span>
                                            </div>
                                            <div class="progress-labels">
                                                <span class="progress-label">Terkumpul</span>
                                                <span class="progress-amount text-<?= $progress >= 100 ? 'success' : 'primary' ?>">
                                                    <?= formatRupiah($target['jumlah_terkumpul']) ?>
                                                </span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="target-timeline">
                                        <div class="timeline-item">
                                            <i class="fas fa-calendar-start text-success"></i>
                                            <div class="timeline-content">
                                                <small class="timeline-label">Mulai</small>
                                                <div class="timeline-date"><?= formatTanggal($target['tanggal_mulai']) ?></div>
                                            </div>
                                        </div>
                                        <div class="timeline-divider"></div>
                                        <div class="timeline-item">
                                            <i class="fas fa-flag-checkered text-<?= $isOverdue ? 'danger' : 'warning' ?>"></i>
                                            <div class="timeline-content">
                                                <small class="timeline-label">Target</small>
                                                <div class="timeline-date"><?= formatTanggal($target['tanggal_selesai']) ?></div>
                                                <?php if ($target['status'] === 'aktif'): ?>
                                                    <small class="text-<?= $isOverdue ? 'danger' : 'muted' ?>">
                                                        <?= $isOverdue ? 'Terlambat ' . $daysRemaining . ' hari' : $daysRemaining . ' hari lagi' ?>
                                                    </small>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>

                                    <?php if ($target['status'] === 'aktif'): ?>
                                    <div class="target-actions">
                                        <button type="button"
                                                class="btn btn-success flex-grow-1"
                                                data-bs-toggle="modal"
                                                data-bs-target="#updateProgressModal"
                                                data-id="<?= $target['id'] ?>"
                                                data-nama="<?= htmlspecialchars($target['nama']) ?>"
                                                data-terkumpul="<?= $target['jumlah_terkumpul'] ?>">
                                            <i class="fas fa-sync-alt me-2"></i>Update Progress
                                        </button>
                                        <button type="button"
                                                class="btn btn-outline-danger"
                                                data-bs-toggle="modal"
                                                data-bs-target="#deleteTargetModal"
                                                data-id="<?= $target['id'] ?>"
                                                data-nama="<?= htmlspecialchars($target['nama']) ?>">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Target Modal -->
<div class="modal fade" id="addTargetModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow">
            <div class="modal-header border-0">
                <h5 class="modal-title">Tambah Target Baru</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="" method="POST" class="needs-validation" novalidate>
                <div class="modal-body">
                    <input type="hidden" name="action" value="add">
                    
                    <div class="mb-3">
                        <label class="form-label">Nama Target</label>
                        <input type="text" class="form-control" name="nama" required>
                        <div class="invalid-feedback">Nama target harus diisi</div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Jumlah Target</label>
                        <div class="input-group">
                            <span class="input-group-text">Rp</span>
                            <input type="number" class="form-control" name="jumlah_target" required min="0">
                        </div>
                        <div class="invalid-feedback">Jumlah target harus diisi dan lebih dari 0</div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Tanggal Mulai</label>
                            <input type="date" class="form-control" name="tanggal_mulai" required>
                            <div class="invalid-feedback">Tanggal mulai harus diisi</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Tanggal Selesai</label>
                            <input type="date" class="form-control" name="tanggal_selesai" required>
                            <div class="invalid-feedback">Tanggal selesai harus diisi</div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Simpan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Update Progress Modal -->
<div class="modal fade" id="updateProgressModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow">
            <div class="modal-header border-0">
                <h5 class="modal-title">Update Progress</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="" method="POST" class="needs-validation" novalidate>
                <div class="modal-body">
                    <input type="hidden" name="action" value="update_progress">
                    <input type="hidden" name="id" id="updateProgressId">
                    
                    <div class="mb-3">
                        <label class="form-label">Target</label>
                        <input type="text" class="form-control" id="updateProgressNama" readonly>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Jumlah Terkumpul</label>
                        <div class="input-group">
                            <span class="input-group-text">Rp</span>
                            <input type="number" class="form-control" name="jumlah_terkumpul" id="updateProgressTerkumpul" required min="0">
                        </div>
                        <div class="invalid-feedback">Jumlah terkumpul harus diisi dan lebih dari 0</div>
                    </div>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Update</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Target Modal -->
<div class="modal fade" id="deleteTargetModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow">
            <div class="modal-header border-0">
                <h5 class="modal-title">Hapus Target</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="" method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="id" id="deleteTargetId">
                    <div class="text-center mb-4">
                        <i class="fas fa-exclamation-triangle text-warning fa-3x mb-3"></i>
                        <p class="mb-0">Apakah Anda yakin ingin menghapus target "<span id="deleteTargetNama" class="fw-bold"></span>"?</p>
                    </div>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-danger">Hapus</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php include 'includes/views/layouts/footer.php'; ?>

<style>
/* Modern Target Cards */
.modern-target-card {
    background: white;
    border-radius: 1.5rem;
    padding: 2rem;
    box-shadow: 0 5px 25px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
    border: 2px solid transparent;
}

.modern-target-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.15);
    border-color: #e9ecef;
}

.target-card-header {
    margin-bottom: 2rem;
}

.target-info {
    display: flex;
    justify-content: between;
    align-items: flex-start;
    gap: 1rem;
}

.target-title {
    font-weight: 700;
    color: #2c3e50;
    margin: 0;
    flex: 1;
    font-size: 1.25rem;
    line-height: 1.3;
}

.target-status {
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-weight: 600;
    font-size: 0.85rem;
    white-space: nowrap;
}

/* Progress Section */
.target-progress-section {
    margin-bottom: 2rem;
    flex: 1;
}

.progress-info {
    background: #f8f9fa;
    border-radius: 1rem;
    padding: 1.5rem;
}

.progress-labels {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 0.75rem;
}

.progress-labels:last-child {
    margin-bottom: 0;
    margin-top: 0.75rem;
}

.progress-label {
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 500;
}

.progress-amount {
    font-weight: 700;
    color: #2c3e50;
    font-size: 1rem;
}

.progress-bar-container {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin: 1rem 0;
}

.modern-progress {
    height: 12px;
    border-radius: 10px;
    background: #e9ecef;
    flex: 1;
    overflow: hidden;
}

.modern-progress .progress-bar {
    border-radius: 10px;
    transition: width 0.6s ease;
}

.progress-percentage {
    font-weight: 700;
    color: #2c3e50;
    font-size: 0.9rem;
    min-width: 45px;
    text-align: right;
}

/* Timeline */
.target-timeline {
    display: flex;
    align-items: center;
    background: #f8f9fa;
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.timeline-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex: 1;
}

.timeline-item i {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: white;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    font-size: 1rem;
}

.timeline-content {
    flex: 1;
}

.timeline-label {
    display: block;
    color: #6c757d;
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.25rem;
}

.timeline-date {
    font-weight: 600;
    color: #2c3e50;
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.timeline-divider {
    width: 40px;
    height: 2px;
    background: linear-gradient(90deg, #dee2e6 0%, #adb5bd 50%, #dee2e6 100%);
    margin: 0 1rem;
    border-radius: 2px;
}

/* Actions */
.target-actions {
    display: flex;
    gap: 0.75rem;
    margin-top: auto;
}

.target-actions .btn {
    border-radius: 0.75rem;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    transition: all 0.3s ease;
}

.target-actions .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

/* Empty State */
.empty-state {
    background: white;
    border-radius: 1.5rem;
    padding: 4rem 2rem;
    box-shadow: 0 5px 25px rgba(0,0,0,0.08);
}

/* Modal Improvements */
.modal-content {
    border-radius: 1.5rem;
    border: none;
    box-shadow: 0 20px 60px rgba(0,0,0,0.2);
}

.modal-header {
    border-bottom: 1px solid #f1f3f4;
    padding: 2rem 2rem 1rem;
}

.modal-body {
    padding: 1rem 2rem;
}

.modal-footer {
    border-top: 1px solid #f1f3f4;
    padding: 1rem 2rem 2rem;
}

.form-control {
    border-radius: 0.75rem;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #3498db;
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
    transform: translateY(-1px);
}

.input-group-text {
    border-radius: 0.75rem 0 0 0.75rem;
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-right: none;
    font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
    .modern-target-card {
        padding: 1.5rem;
    }

    .target-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
    }

    .target-title {
        font-size: 1.1rem;
    }

    .progress-info,
    .target-timeline {
        padding: 1.25rem;
    }

    .timeline-item {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }

    .timeline-divider {
        width: 2px;
        height: 30px;
        margin: 0.5rem 0;
    }

    .target-actions {
        flex-direction: column;
    }

    .target-actions .btn {
        padding: 0.6rem 1.25rem;
    }

    .modal-header,
    .modal-body,
    .modal-footer {
        padding-left: 1.5rem;
        padding-right: 1.5rem;
    }
}

@media (max-width: 576px) {
    .modern-target-card {
        padding: 1.25rem;
    }

    .target-title {
        font-size: 1rem;
    }

    .progress-info,
    .target-timeline {
        padding: 1rem;
    }

    .timeline-item i {
        width: 35px;
        height: 35px;
        font-size: 0.9rem;
    }
}

/* Animation */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modern-target-card {
    animation: slideInUp 0.6s ease-out;
}

.modern-target-card:nth-child(1) { animation-delay: 0.1s; }
.modern-target-card:nth-child(2) { animation-delay: 0.2s; }
.modern-target-card:nth-child(3) { animation-delay: 0.3s; }
.modern-target-card:nth-child(4) { animation-delay: 0.4s; }
</style>

<script>
    // Update Progress Modal
    document.getElementById('updateProgressModal').addEventListener('show.bs.modal', function (event) {
        const button = event.relatedTarget;
        const id = button.getAttribute('data-id');
        const nama = button.getAttribute('data-nama');
        const terkumpul = button.getAttribute('data-terkumpul');

        document.getElementById('updateProgressId').value = id;
        document.getElementById('updateProgressNama').value = nama;
        document.getElementById('updateProgressTerkumpul').value = terkumpul;
    });

    // Delete Target Modal
    document.getElementById('deleteTargetModal').addEventListener('show.bs.modal', function (event) {
        const button = event.relatedTarget;
        const id = button.getAttribute('data-id');
        const nama = button.getAttribute('data-nama');

        document.getElementById('deleteTargetId').value = id;
        document.getElementById('deleteTargetNama').textContent = nama;
    });
</script> 