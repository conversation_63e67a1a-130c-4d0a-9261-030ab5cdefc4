/**
 * Enhanced Modern JavaScript Framework
 * Comprehensive interactivity for all page components
 */

class EnhancedModernFramework {
    constructor() {
        this.init();
    }

    init() {
        this.setupTableFeatures();
        this.setupAnimations();
        this.setupFormEnhancements();
        this.setupResponsiveFeatures();
        this.setupAccessibility();
        this.setupPerformanceOptimizations();
    }

    // Enhanced table functionality
    setupTableFeatures() {
        // Table search functionality
        window.filterTable = (tableId, searchTerm) => {
            const table = document.getElementById(tableId);
            if (!table) return;

            const tbody = table.querySelector('tbody');
            const rows = tbody.querySelectorAll('tr:not(.empty-row)');
            let visibleCount = 0;

            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                const isVisible = text.includes(searchTerm.toLowerCase());
                
                row.style.display = isVisible ? '' : 'none';
                if (isVisible) visibleCount++;
            });

            // Update showing count
            const showingElement = document.getElementById(`${tableId}_showing`);
            if (showingElement) {
                showingElement.textContent = visibleCount;
            }

            // Show/hide empty state
            const emptyRow = tbody.querySelector('.empty-row');
            if (emptyRow) {
                emptyRow.style.display = visibleCount === 0 ? '' : 'none';
            }
        };

        // Table sorting functionality
        window.sortTable = (tableId, headerElement) => {
            const table = document.getElementById(tableId);
            if (!table) return;

            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr:not(.empty-row)'));
            const columnIndex = Array.from(headerElement.parentNode.children).indexOf(headerElement);
            
            // Determine sort direction
            const currentSort = headerElement.dataset.sort || 'none';
            const newSort = currentSort === 'asc' ? 'desc' : 'asc';
            
            // Reset all sort indicators
            table.querySelectorAll('.sort-icon').forEach(icon => {
                icon.className = 'fas fa-sort sort-icon';
            });
            
            // Set new sort indicator
            const sortIcon = headerElement.querySelector('.sort-icon');
            sortIcon.className = `fas fa-sort-${newSort === 'asc' ? 'up' : 'down'} sort-icon`;
            headerElement.dataset.sort = newSort;

            // Sort rows
            rows.sort((a, b) => {
                const aValue = a.children[columnIndex].textContent.trim();
                const bValue = b.children[columnIndex].textContent.trim();
                
                // Try to parse as numbers
                const aNum = parseFloat(aValue.replace(/[^\d.-]/g, ''));
                const bNum = parseFloat(bValue.replace(/[^\d.-]/g, ''));
                
                let comparison = 0;
                if (!isNaN(aNum) && !isNaN(bNum)) {
                    comparison = aNum - bNum;
                } else {
                    comparison = aValue.localeCompare(bValue);
                }
                
                return newSort === 'asc' ? comparison : -comparison;
            });

            // Reorder rows in DOM
            rows.forEach(row => tbody.appendChild(row));
        };

        // Initialize table pagination
        this.initTablePagination();
    }

    initTablePagination() {
        document.querySelectorAll('[id$="_pagination"]').forEach(paginationContainer => {
            const tableId = paginationContainer.id.replace('_pagination', '');
            const table = document.getElementById(tableId);
            if (!table) return;

            const rowsPerPage = 10;
            const tbody = table.querySelector('tbody');
            const rows = tbody.querySelectorAll('tr:not(.empty-row)');
            
            if (rows.length <= rowsPerPage) {
                paginationContainer.style.display = 'none';
                return;
            }

            const totalPages = Math.ceil(rows.length / rowsPerPage);
            let currentPage = 1;

            const showPage = (page) => {
                const start = (page - 1) * rowsPerPage;
                const end = start + rowsPerPage;

                rows.forEach((row, index) => {
                    row.style.display = (index >= start && index < end) ? '' : 'none';
                });

                // Update pagination buttons
                updatePaginationButtons();
            };

            const updatePaginationButtons = () => {
                paginationContainer.innerHTML = '';
                
                // Previous button
                const prevLi = document.createElement('li');
                prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
                prevLi.innerHTML = `<a class="page-link" href="#" data-page="${currentPage - 1}">Previous</a>`;
                paginationContainer.appendChild(prevLi);

                // Page numbers
                for (let i = 1; i <= totalPages; i++) {
                    if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
                        const li = document.createElement('li');
                        li.className = `page-item ${i === currentPage ? 'active' : ''}`;
                        li.innerHTML = `<a class="page-link" href="#" data-page="${i}">${i}</a>`;
                        paginationContainer.appendChild(li);
                    } else if (i === currentPage - 3 || i === currentPage + 3) {
                        const li = document.createElement('li');
                        li.className = 'page-item disabled';
                        li.innerHTML = '<span class="page-link">...</span>';
                        paginationContainer.appendChild(li);
                    }
                }

                // Next button
                const nextLi = document.createElement('li');
                nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
                nextLi.innerHTML = `<a class="page-link" href="#" data-page="${currentPage + 1}">Next</a>`;
                paginationContainer.appendChild(nextLi);
            };

            // Handle pagination clicks
            paginationContainer.addEventListener('click', (e) => {
                e.preventDefault();
                if (e.target.tagName === 'A' && !e.target.parentNode.classList.contains('disabled')) {
                    const page = parseInt(e.target.dataset.page);
                    if (page >= 1 && page <= totalPages) {
                        currentPage = page;
                        showPage(currentPage);
                    }
                }
            });

            // Initialize first page
            showPage(1);
        });
    }

    // Setup animations
    setupAnimations() {
        // Intersection Observer for scroll animations
        if ('IntersectionObserver' in window) {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate-fade-in');
                        observer.unobserve(entry.target);
                    }
                });
            }, {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            });

            // Observe elements that should animate
            document.querySelectorAll('.enhanced-stats-card, .enhanced-table-container, .enhanced-filter-container').forEach(el => {
                observer.observe(el);
            });
        }

        // Counter animation for stats
        this.animateCounters();

        // Progress bar animations
        this.animateProgressBars();
    }

    animateCounters() {
        document.querySelectorAll('.stats-value[data-value]').forEach(counter => {
            const target = parseFloat(counter.dataset.value.replace(/[^\d.-]/g, '')) || 0;
            const duration = 2000;
            const start = performance.now();
            const originalText = counter.textContent;

            const animate = (currentTime) => {
                const elapsed = currentTime - start;
                const progress = Math.min(elapsed / duration, 1);
                
                // Easing function
                const easeOutQuart = 1 - Math.pow(1 - progress, 4);
                const current = target * easeOutQuart;
                
                // Format the number
                if (originalText.includes('Rp')) {
                    counter.textContent = 'Rp ' + Math.floor(current).toLocaleString('id-ID');
                } else if (originalText.includes('%')) {
                    counter.textContent = Math.floor(current) + '%';
                } else {
                    counter.textContent = Math.floor(current).toLocaleString('id-ID');
                }

                if (progress < 1) {
                    requestAnimationFrame(animate);
                }
            };

            // Start animation when element is visible
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        requestAnimationFrame(animate);
                        observer.unobserve(entry.target);
                    }
                });
            });

            observer.observe(counter);
        });
    }

    animateProgressBars() {
        document.querySelectorAll('.progress-bar[data-progress]').forEach(bar => {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const progress = entry.target.dataset.progress;
                        setTimeout(() => {
                            entry.target.style.width = progress + '%';
                        }, 500);
                        observer.unobserve(entry.target);
                    }
                });
            });

            observer.observe(bar);
        });
    }

    // Form enhancements
    setupFormEnhancements() {
        // Auto-save form data
        this.setupAutoSave();

        // Form validation enhancements
        this.setupFormValidation();

        // Dynamic form fields
        this.setupDynamicFields();
    }

    setupAutoSave() {
        const forms = document.querySelectorAll('form[data-autosave]');
        forms.forEach(form => {
            const formId = form.id || 'form_' + Date.now();
            const inputs = form.querySelectorAll('input, select, textarea');

            inputs.forEach(input => {
                input.addEventListener('input', () => {
                    const formData = new FormData(form);
                    const data = Object.fromEntries(formData);
                    localStorage.setItem(`autosave_${formId}`, JSON.stringify(data));
                });
            });

            // Restore saved data
            const savedData = localStorage.getItem(`autosave_${formId}`);
            if (savedData) {
                try {
                    const data = JSON.parse(savedData);
                    Object.keys(data).forEach(key => {
                        const input = form.querySelector(`[name="${key}"]`);
                        if (input) {
                            input.value = data[key];
                        }
                    });
                } catch (e) {
                    console.error('Error restoring form data:', e);
                }
            }
        });
    }

    setupFormValidation() {
        // Real-time validation
        document.querySelectorAll('.form-control, .form-select').forEach(input => {
            input.addEventListener('blur', () => {
                this.validateField(input);
            });

            input.addEventListener('input', () => {
                if (input.classList.contains('is-invalid')) {
                    this.validateField(input);
                }
            });
        });
    }

    validateField(field) {
        const isValid = field.checkValidity();
        field.classList.toggle('is-valid', isValid);
        field.classList.toggle('is-invalid', !isValid);

        // Show/hide feedback
        const feedback = field.parentNode.querySelector('.invalid-feedback');
        if (feedback) {
            feedback.style.display = isValid ? 'none' : 'block';
        }
    }

    setupDynamicFields() {
        // Add dynamic field functionality
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-add-field]')) {
                this.addDynamicField(e.target);
            } else if (e.target.matches('[data-remove-field]')) {
                this.removeDynamicField(e.target);
            }
        });
    }

    addDynamicField(button) {
        const template = document.querySelector(button.dataset.template);
        if (template) {
            const clone = template.content.cloneNode(true);
            const container = document.querySelector(button.dataset.container);
            if (container) {
                container.appendChild(clone);
            }
        }
    }

    removeDynamicField(button) {
        const fieldGroup = button.closest('[data-field-group]');
        if (fieldGroup) {
            fieldGroup.remove();
        }
    }

    // Responsive features
    setupResponsiveFeatures() {
        // Mobile menu handling
        this.setupMobileMenu();

        // Responsive tables
        this.setupResponsiveTables();

        // Touch gestures
        this.setupTouchGestures();
    }

    setupMobileMenu() {
        const toggles = document.querySelectorAll('[data-mobile-toggle]');
        toggles.forEach(toggle => {
            toggle.addEventListener('click', () => {
                const target = document.querySelector(toggle.dataset.target);
                if (target) {
                    target.classList.toggle('show');
                }
            });
        });
    }

    setupResponsiveTables() {
        const tables = document.querySelectorAll('.enhanced-table');
        tables.forEach(table => {
            if (window.innerWidth < 768) {
                this.makeTableResponsive(table);
            }
        });

        window.addEventListener('resize', () => {
            tables.forEach(table => {
                if (window.innerWidth < 768) {
                    this.makeTableResponsive(table);
                } else {
                    this.restoreTable(table);
                }
            });
        });
    }

    makeTableResponsive(table) {
        if (table.classList.contains('responsive-applied')) return;

        const headers = Array.from(table.querySelectorAll('thead th')).map(th => th.textContent);
        const rows = table.querySelectorAll('tbody tr');

        rows.forEach(row => {
            const cells = row.querySelectorAll('td');
            cells.forEach((cell, index) => {
                if (headers[index]) {
                    cell.setAttribute('data-label', headers[index]);
                }
            });
        });

        table.classList.add('responsive-applied');
    }

    restoreTable(table) {
        table.classList.remove('responsive-applied');
        table.querySelectorAll('td[data-label]').forEach(cell => {
            cell.removeAttribute('data-label');
        });
    }

    setupTouchGestures() {
        // Swipe gestures for mobile
        let startX, startY, endX, endY;

        document.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
            startY = e.touches[0].clientY;
        });

        document.addEventListener('touchend', (e) => {
            endX = e.changedTouches[0].clientX;
            endY = e.changedTouches[0].clientY;
            this.handleSwipe();
        });
    }

    handleSwipe() {
        const deltaX = endX - startX;
        const deltaY = endY - startY;
        const minSwipeDistance = 50;

        if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > minSwipeDistance) {
            if (deltaX > 0) {
                // Swipe right
                this.handleSwipeRight();
            } else {
                // Swipe left
                this.handleSwipeLeft();
            }
        }
    }

    handleSwipeRight() {
        // Open sidebar on mobile
        const sidebar = document.querySelector('.sidebar');
        if (sidebar && window.innerWidth < 768) {
            sidebar.classList.add('show');
        }
    }

    handleSwipeLeft() {
        // Close sidebar on mobile
        const sidebar = document.querySelector('.sidebar');
        if (sidebar && window.innerWidth < 768) {
            sidebar.classList.remove('show');
        }
    }

    // Accessibility features
    setupAccessibility() {
        // Keyboard navigation
        this.setupKeyboardNavigation();

        // Focus management
        this.setupFocusManagement();

        // Screen reader support
        this.setupScreenReaderSupport();
    }

    setupKeyboardNavigation() {
        document.addEventListener('keydown', (e) => {
            // Escape key to close modals/dropdowns
            if (e.key === 'Escape') {
                const openModal = document.querySelector('.modal.show');
                if (openModal) {
                    const modal = bootstrap.Modal.getInstance(openModal);
                    if (modal) modal.hide();
                }

                const openDropdown = document.querySelector('.dropdown-menu.show');
                if (openDropdown) {
                    openDropdown.classList.remove('show');
                }
            }

            // Tab navigation for tables
            if (e.key === 'Tab' && e.target.matches('.enhanced-table td')) {
                e.preventDefault();
                this.navigateTableCell(e.target, e.shiftKey ? -1 : 1);
            }
        });
    }

    navigateTableCell(currentCell, direction) {
        const row = currentCell.parentNode;
        const table = row.closest('table');
        const cells = Array.from(table.querySelectorAll('td'));
        const currentIndex = cells.indexOf(currentCell);
        const nextIndex = currentIndex + direction;

        if (nextIndex >= 0 && nextIndex < cells.length) {
            cells[nextIndex].focus();
        }
    }

    setupFocusManagement() {
        // Focus trap for modals
        document.querySelectorAll('.modal').forEach(modal => {
            modal.addEventListener('shown.bs.modal', () => {
                const focusableElements = modal.querySelectorAll(
                    'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
                );
                if (focusableElements.length > 0) {
                    focusableElements[0].focus();
                }
            });
        });
    }

    setupScreenReaderSupport() {
        // Add ARIA labels to interactive elements
        document.querySelectorAll('.action-btn:not([aria-label])').forEach(btn => {
            const text = btn.textContent.trim() || btn.title;
            if (text) {
                btn.setAttribute('aria-label', text);
            }
        });

        // Live regions for dynamic content
        if (!document.querySelector('#live-region')) {
            const liveRegion = document.createElement('div');
            liveRegion.id = 'live-region';
            liveRegion.setAttribute('aria-live', 'polite');
            liveRegion.setAttribute('aria-atomic', 'true');
            liveRegion.style.cssText = 'position: absolute; left: -10000px; width: 1px; height: 1px; overflow: hidden;';
            document.body.appendChild(liveRegion);
        }
    }

    // Performance optimizations
    setupPerformanceOptimizations() {
        // Lazy loading for images
        this.setupLazyLoading();

        // Debounced resize handler
        this.setupDebouncedResize();

        // Virtual scrolling for large tables
        this.setupVirtualScrolling();
    }

    setupLazyLoading() {
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('loading');
                        imageObserver.unobserve(img);
                    }
                });
            });

            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });
        }
    }

    setupDebouncedResize() {
        let resizeTimeout;
        window.addEventListener('resize', () => {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(() => {
                this.handleResize();
            }, 250);
        });
    }

    handleResize() {
        // Recalculate responsive features
        this.setupResponsiveTables();
        
        // Update chart sizes if present
        if (window.chartManager) {
            window.chartManager.getAllCharts().forEach(chart => {
                chart.resize();
            });
        }
    }

    setupVirtualScrolling() {
        document.querySelectorAll('.enhanced-table[data-virtual]').forEach(table => {
            // Implementation for virtual scrolling would go here
            // This is a placeholder for very large datasets
        });
    }

    // Public API methods
    showNotification(message, type = 'info') {
        if (window.showNotification) {
            window.showNotification({
                type: type,
                title: type.charAt(0).toUpperCase() + type.slice(1),
                message: message
            });
        }
    }

    updateLiveRegion(message) {
        const liveRegion = document.getElementById('live-region');
        if (liveRegion) {
            liveRegion.textContent = message;
        }
    }
}

// Initialize the framework
document.addEventListener('DOMContentLoaded', () => {
    window.enhancedModern = new EnhancedModernFramework();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = EnhancedModernFramework;
}

console.log('🚀 Enhanced Modern Framework Loaded - All pages now responsive with modern UI/UX');
