/**
 * Enhanced Modern CSS Framework
 * Comprehensive responsive styling for all page components
 * Mobile-first approach with advanced UI/UX patterns
 */

/* ===== CSS VARIABLES ===== */
:root {
    /* Colors */
    --primary: #0d6efd;
    --secondary: #6c757d;
    --success: #198754;
    --info: #0dcaf0;
    --warning: #ffc107;
    --danger: #dc3545;
    --light: #f8f9fa;
    --dark: #212529;

    /* Container widths */
    --container-max-width: 100%;
    --content-max-width: 1400px;
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-success: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    --gradient-warning: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --gradient-info: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    
    /* Spacing - Increased for better zoom 100% visibility */
    --spacing-xs: 0.5rem;
    --spacing-sm: 1rem;
    --spacing-md: 1.5rem;
    --spacing-lg: 2.5rem;
    --spacing-xl: 4rem;

    /* Typography - Increased for better readability */
    --font-size-xs: 0.875rem;
    --font-size-sm: 1rem;
    --font-size-base: 1.125rem;
    --font-size-lg: 1.25rem;
    --font-size-xl: 1.5rem;

    /* Border radius */
    --border-radius-sm: 0.375rem;
    --border-radius-md: 0.5rem;
    --border-radius-lg: 0.75rem;
    --border-radius-xl: 1rem;
    
    /* Shadows */
    --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --shadow-md: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
    
    /* Typography */
    --font-family-base: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    
    /* Transitions */
    --transition-base: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 0.15s ease-in-out;
}

/* ===== BASE STYLES ===== */
body {
    font-family: var(--font-family-base);
    line-height: 1.6;
    color: var(--dark);
    background-color: #f8f9fa;
    font-size: var(--font-size-base);
    min-height: 100vh;
}

h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: var(--spacing-sm);
}

h1 { font-size: 3rem; }
h2 { font-size: 2.5rem; }
h3 { font-size: 2rem; }
h4 { font-size: 1.75rem; }
h5 { font-size: 1.5rem; }
h6 { font-size: 1.25rem; }

p {
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-base);
}

/* ===== ENHANCED PAGE HEADER ===== */
.enhanced-page-header {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-bottom: 2px solid #e9ecef;
    padding: 3rem 0;
    margin-bottom: 3rem;
    position: relative;
    overflow: hidden;
    width: 100%;
    min-height: 160px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.enhanced-page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
}

.modern-breadcrumb {
    background: none;
    padding: 0;
    margin: 0;
    font-size: var(--font-size-sm);
}

.modern-breadcrumb .breadcrumb-item {
    color: var(--secondary);
}

.modern-breadcrumb .breadcrumb-link {
    color: var(--primary);
    text-decoration: none;
    transition: var(--transition-fast);
}

.modern-breadcrumb .breadcrumb-link:hover {
    color: var(--info);
    text-decoration: underline;
}

.page-title {
    font-size: 3.5rem;
    font-weight: 700;
    color: var(--dark);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 1.5rem;
    line-height: 1.2;
}

.title-icon {
    width: 6rem;
    height: 6rem;
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    background: rgba(13, 110, 253, 0.1);
    flex-shrink: 0;
    box-shadow: 0 4px 12px rgba(13, 110, 253, 0.2);
}

.page-subtitle {
    color: var(--secondary);
    margin: var(--spacing-xs) 0 0 0;
    font-size: var(--font-size-xl);
    font-weight: 400;
    line-height: 1.4;
}

.page-actions {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    flex-wrap: wrap;
    justify-content: flex-end;
    min-height: 70px;
}

.action-btn {
    border-radius: var(--border-radius-md);
    font-weight: 500;
    padding: 1rem 2rem;
    transition: var(--transition-base);
    border: none;
    position: relative;
    overflow: hidden;
    min-width: 150px;
    min-height: 50px;
    font-size: 1.125rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.action-btn:hover::before {
    left: 100%;
}

/* ===== ENHANCED STATS CARDS ===== */
.enhanced-stats-section {
    margin-bottom: var(--spacing-xl);
}

.enhanced-stats-card {
    background: white;
    border-radius: var(--border-radius-lg);
    padding: 3rem;
    box-shadow: var(--shadow-md);
    border: 1px solid #e9ecef;
    transition: var(--transition-base);
    position: relative;
    overflow: hidden;
    height: 100%;
    min-height: 220px;
    margin-bottom: 2rem;
}

.enhanced-stats-card.gradient {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
}

.enhanced-stats-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.enhanced-stats-card.animate-card {
    opacity: 0;
    transform: translateY(20px);
    animation: slideInUp 0.6s ease-out forwards;
}

@keyframes slideInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.stats-card-body {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.stats-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-md);
}

.stats-icon-container {
    position: relative;
}

.stats-icon {
    width: 6rem;
    height: 6rem;
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.5rem;
    color: white;
    position: relative;
    overflow: hidden;
    flex-shrink: 0;
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

.stats-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.3));
    opacity: 0;
    transition: var(--transition-base);
}

.enhanced-stats-card:hover .stats-icon::before {
    opacity: 1;
}

.stats-trend {
    font-size: var(--font-size-sm);
    font-weight: 600;
}

.trend-indicator {
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius-sm);
    color: white;
}

.trend-up {
    background: var(--success);
}

.trend-down {
    background: var(--danger);
}

.stats-content {
    flex: 1;
}

.stats-value {
    font-size: 3.5rem;
    font-weight: 700;
    color: var(--dark);
    margin: 0 0 0.75rem 0;
    line-height: 1.1;
    word-break: break-word;
}

.stats-label {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--dark);
    margin: 0 0 var(--spacing-xs) 0;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stats-subtitle {
    color: var(--secondary);
    font-size: var(--font-size-base);
    margin-top: var(--spacing-xs);
}

.stats-progress {
    margin-top: var(--spacing-md);
}

.stats-progress .progress {
    height: 0.5rem;
    border-radius: var(--border-radius-sm);
    background: #e9ecef;
}

.stats-progress .progress-bar {
    border-radius: var(--border-radius-sm);
    transition: width 1s ease-in-out;
}

/* ===== ENHANCED DATA TABLE ===== */
.enhanced-table-container {
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    border: 2px solid #e9ecef;
    width: 100%;
    margin: 0 auto 3rem auto;
    min-width: 0;
}

.table-controls {
    padding: 2.5rem 3rem;
    background: #f8f9fa;
    border-bottom: 2px solid #e9ecef;
    min-height: 100px;
}

.table-search .input-group-text {
    background: white;
    border-right: none;
    color: var(--secondary);
}

.table-search .form-control {
    border-left: none;
    box-shadow: none;
}

.table-search .form-control:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.enhanced-table {
    margin: 0;
    font-size: 1.125rem;
    width: 100%;
    table-layout: auto;
}

.table-header th {
    background: #f8f9fa;
    border-bottom: 3px solid #dee2e6;
    font-weight: 600;
    color: var(--dark);
    padding: 1.5rem 2rem;
    position: relative;
    white-space: nowrap;
    min-width: 150px;
    font-size: 1.125rem;
}

.table-header th.sortable {
    cursor: pointer;
    user-select: none;
    transition: var(--transition-fast);
}

.table-header th.sortable:hover {
    background: #e9ecef;
}

.sort-icon {
    margin-left: var(--spacing-xs);
    opacity: 0.5;
    transition: var(--transition-fast);
}

.table-header th.sortable:hover .sort-icon {
    opacity: 1;
}

.enhanced-table tbody tr {
    transition: var(--transition-fast);
}

.enhanced-table tbody tr:hover {
    background: rgba(13, 110, 253, 0.05);
}

.enhanced-table td {
    padding: 1.25rem 2rem;
    vertical-align: middle;
    border-bottom: 1px solid #e9ecef;
    word-wrap: break-word;
    min-width: 120px;
    font-size: 1.125rem;
}

.empty-state {
    padding: var(--spacing-xl);
}

.empty-state i {
    opacity: 0.5;
}

.action-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    min-height: 50px;
}

.action-buttons .btn {
    padding: 1rem 1.5rem;
    border-radius: var(--border-radius-sm);
    font-size: 1rem;
    min-width: 120px;
    min-height: 48px;
    margin: 0.25rem;
}

/* ===== ENHANCED FILTER ===== */
.enhanced-filter-container .card {
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
}

.enhanced-filter-container .card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
}

.filter-form .form-label {
    font-weight: 500;
    color: var(--dark);
    margin-bottom: var(--spacing-xs);
}

.filter-form .form-control,
.filter-form .form-select {
    border-radius: var(--border-radius-md);
    border: 2px solid #ced4da;
    transition: var(--transition-fast);
    padding: 1rem 1.25rem;
    font-size: 1.125rem;
    min-height: 50px;
}

.filter-form .form-control:focus,
.filter-form .form-select:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.filter-actions {
    display: flex;
    gap: var(--spacing-sm);
    justify-content: flex-end;
    margin-top: var(--spacing-md);
}

/* ===== RESPONSIVE DESIGN ===== */

/* Content Wrapper - Main Layout Container */
.content-wrapper {
    min-height: calc(100vh - 120px);
    padding: 0;
    margin: 0;
    width: 100%;
    overflow-x: hidden;
}

.main-content-container {
    width: 100%;
    max-width: 100%;
    padding: 2rem;
    margin: 0;
    min-width: 0;
}

/* Enhanced Content Section */
.enhanced-content-section {
    width: 100%;
    margin-bottom: var(--spacing-lg);
}

.enhanced-content {
    width: 100%;
    padding: 0;
    margin: 0;
}

/* Responsive Container Adjustments */
.container-fluid {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
    max-width: 100%;
    width: 100%;
    margin: 0 auto;
}

/* Mobile First - Extra Small devices (portrait phones, less than 576px) */
@media (max-width: 575.98px) {
    .content-wrapper {
        padding: 0;
    }

    .main-content-container {
        padding: var(--spacing-sm);
    }

    .container-fluid {
        padding-left: var(--spacing-xs);
        padding-right: var(--spacing-xs);
    }

    .enhanced-page-header {
        padding: var(--spacing-sm) 0;
        margin-bottom: var(--spacing-md);
    }

    .page-title {
        font-size: 1.25rem;
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-xs);
    }

    .title-icon {
        width: 2rem;
        height: 2rem;
        font-size: 1rem;
    }

    .page-subtitle {
        font-size: var(--font-size-sm);
        margin-top: var(--spacing-xs);
    }

    .page-actions {
        width: 100%;
        margin-top: var(--spacing-sm);
        justify-content: stretch;
        flex-direction: column;
    }

    .action-btn {
        width: 100%;
        margin-bottom: var(--spacing-xs);
        justify-content: center;
    }

    .enhanced-stats-card {
        padding: var(--spacing-sm);
        margin-bottom: var(--spacing-sm);
    }

    .stats-value {
        font-size: 1.25rem;
    }

    .stats-icon {
        width: 2.5rem;
        height: 2.5rem;
        font-size: 1.25rem;
    }

    .enhanced-table-container {
        margin: 0 -var(--spacing-xs);
        border-radius: 0;
        border-left: none;
        border-right: none;
    }

    .table-responsive {
        border-radius: 0;
    }

    .enhanced-table {
        font-size: 0.75rem;
        min-width: 600px;
    }

    .enhanced-table th,
    .enhanced-table td {
        padding: var(--spacing-xs);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 150px;
    }

    .table-responsive {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    .action-buttons {
        flex-direction: column;
        gap: var(--spacing-xs);
    }

    .action-buttons .btn {
        width: 100%;
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }

    .btn-text-mobile {
        font-weight: bold;
        font-size: 1rem;
    }

    .action-btn .btn-text {
        display: none;
    }
}

/* Small devices (landscape phones, 576px and up) */
@media (min-width: 576px) and (max-width: 767.98px) {
    .main-content-container {
        padding: var(--spacing-md);
    }

    .page-title {
        font-size: 1.5rem;
    }

    .page-actions {
        flex-direction: row;
        flex-wrap: wrap;
        gap: var(--spacing-sm);
    }

    .action-btn {
        flex: 1;
        min-width: auto;
    }

    .enhanced-stats-card {
        padding: var(--spacing-md);
    }

    .stats-value {
        font-size: 1.5rem;
    }
}

/* Medium devices (tablets, 768px and up) */
@media (min-width: 768px) and (max-width: 991.98px) {
    .content-wrapper {
        padding: 0 var(--spacing-sm);
    }

    .main-content-container {
        padding: var(--spacing-lg);
    }

    .page-title {
        font-size: 1.75rem;
    }

    .page-actions {
        margin-top: 0;
        justify-content: flex-end;
    }

    .enhanced-stats-card {
        padding: var(--spacing-lg);
    }

    .stats-value {
        font-size: 1.75rem;
    }

    .table-controls {
        padding: var(--spacing-lg);
    }

    .enhanced-table th,
    .enhanced-table td {
        padding: var(--spacing-sm);
    }
}

/* Large devices (desktops, 992px and up) */
@media (min-width: 992px) and (max-width: 1199.98px) {
    .content-wrapper {
        padding: 0 1rem;
    }

    .main-content-container {
        padding: 2.5rem;
        max-width: 100%;
        margin: 0;
    }

    .page-title {
        font-size: 2.75rem;
    }

    .enhanced-stats-card {
        padding: 2.5rem;
        min-height: 200px;
    }

    .stats-value {
        font-size: 2.75rem;
    }

    .stats-icon {
        width: 5rem;
        height: 5rem;
        font-size: 2.25rem;
    }

    .title-icon {
        width: 4.5rem;
        height: 4.5rem;
        font-size: 2.25rem;
    }
}

/* Extra large devices (large desktops, 1200px and up) */
@media (min-width: 1200px) {
    .content-wrapper {
        padding: 0 1.5rem;
    }

    .main-content-container {
        padding: 3rem;
        max-width: 100%;
        margin: 0;
    }

    .page-title {
        font-size: 3rem;
    }

    .enhanced-stats-card {
        padding: 3rem;
        min-height: 220px;
    }

    .stats-value {
        font-size: 3rem;
    }

    .stats-icon {
        width: 5.5rem;
        height: 5.5rem;
        font-size: 2.5rem;
    }

    .title-icon {
        width: 5rem;
        height: 5rem;
        font-size: 2.5rem;
    }

    .enhanced-table th,
    .enhanced-table td {
        padding: 1.25rem 1.75rem;
    }

    .action-btn {
        padding: 1rem 2rem;
        min-width: 140px;
        font-size: 1.1rem;
    }

    .table-controls {
        padding: 2rem 2.5rem;
        min-height: 100px;
    }
}

/* Ultra wide screens (1400px and up) */
@media (min-width: 1400px) {
    .content-wrapper {
        padding: 0 2rem;
    }

    .main-content-container {
        padding: 3.5rem;
        max-width: 100%;
        margin: 0;
    }

    .page-title {
        font-size: 3.5rem;
    }

    .enhanced-stats-card {
        padding: 3.5rem;
        min-height: 250px;
    }

    .stats-value {
        font-size: 3.5rem;
    }

    .stats-icon {
        width: 6rem;
        height: 6rem;
        font-size: 3rem;
    }

    .title-icon {
        width: 5.5rem;
        height: 5.5rem;
        font-size: 3rem;
    }

    .enhanced-table th,
    .enhanced-table td {
        padding: 1.5rem 2rem;
    }

    .action-btn {
        padding: 1.25rem 2.5rem;
        min-width: 160px;
        font-size: 1.2rem;
    }

    .table-controls {
        padding: 2.5rem 3rem;
        min-height: 120px;
    }

    .enhanced-page-header {
        padding: 2.5rem 0;
        min-height: 140px;
    }
}

/* ===== GLOBAL RESPONSIVE UTILITIES ===== */

/* Prevent horizontal overflow */
html, body {
    overflow-x: hidden;
    max-width: 100%;
    box-sizing: border-box;
}

*, *::before, *::after {
    box-sizing: border-box;
}

/* Ensure all containers respect viewport width */
.container, .container-fluid, .container-sm, .container-md, .container-lg, .container-xl, .container-xxl {
    max-width: 100%;
    overflow-x: hidden;
    box-sizing: border-box;
}

/* Prevent content from breaking layout */
.row {
    margin-left: 0;
    margin-right: 0;
    max-width: 100%;
}

.col, [class*="col-"] {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
    max-width: 100%;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

/* Responsive images and media */
img, video, iframe {
    max-width: 100%;
    height: auto;
}

/* Responsive text */
.text-responsive {
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
}

/* Hide elements on mobile */
@media (max-width: 575.98px) {
    .d-mobile-none {
        display: none !important;
    }

    .text-mobile-center {
        text-align: center !important;
    }

    .flex-mobile-column {
        flex-direction: column !important;
    }
}

/* Show only on mobile */
@media (min-width: 576px) {
    .d-mobile-only {
        display: none !important;
    }
}

/* Responsive spacing utilities */
@media (max-width: 575.98px) {
    .p-mobile-sm { padding: var(--spacing-sm) !important; }
    .m-mobile-sm { margin: var(--spacing-sm) !important; }
    .px-mobile-xs { padding-left: var(--spacing-xs) !important; padding-right: var(--spacing-xs) !important; }
    .py-mobile-xs { padding-top: var(--spacing-xs) !important; padding-bottom: var(--spacing-xs) !important; }
}

/* Responsive font sizes */
@media (max-width: 575.98px) {
    .fs-mobile-sm { font-size: var(--font-size-sm) !important; }
    .fs-mobile-xs { font-size: var(--font-size-xs) !important; }
}

/* Enhanced scrollbar styling */
.enhanced-scrollbar::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.enhanced-scrollbar::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.enhanced-scrollbar::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.enhanced-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* ===== ENHANCED CARDS AND MODALS ===== */
.card {
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    border: 2px solid #e9ecef;
    margin-bottom: 2.5rem;
}

.card-header {
    padding: 2rem 2.5rem;
    background: #f8f9fa;
    border-bottom: 2px solid #e9ecef;
    font-weight: 600;
    font-size: 1.25rem;
}

.card-body {
    padding: 2.5rem;
}

.modal-dialog {
    max-width: 600px;
    margin: 2rem auto;
}

.modal-content {
    border-radius: var(--border-radius-lg);
    border: none;
    box-shadow: var(--shadow-xl);
}

.modal-header {
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #e9ecef;
}

.modal-body {
    padding: 2rem;
}

.modal-footer {
    padding: 1.5rem 2rem;
    border-top: 1px solid #e9ecef;
}

/* ===== ENHANCED BREADCRUMBS ===== */
.breadcrumb {
    background: transparent;
    padding: 0;
    margin-bottom: 1rem;
    font-size: 1rem;
}

.breadcrumb-item {
    font-size: 1rem;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: ">";
    color: #6c757d;
    font-weight: bold;
}

/* ===== ENHANCED PAGINATION ===== */
.pagination {
    margin-bottom: 0;
}

.page-link {
    padding: 0.75rem 1rem;
    font-size: 1rem;
    border-radius: var(--border-radius-md);
    margin: 0 0.25rem;
    border: 1px solid #dee2e6;
}

.page-link:hover {
    background-color: #e9ecef;
    border-color: #dee2e6;
}

.page-item.active .page-link {
    background-color: var(--primary);
    border-color: var(--primary);
}

/* Legacy mobile support */
@media (max-width: 576px) {
    .enhanced-stats-card {
        padding: var(--spacing-md);
    }

    .stats-icon {
        width: 3rem;
        height: 3rem;
        font-size: 1.25rem;
    }
    
    .stats-value {
        font-size: 1.25rem;
    }
    
    .enhanced-table {
        font-size: var(--font-size-xs);
    }
    
    .enhanced-table th,
    .enhanced-table td {
        padding: var(--spacing-sm);
    }
}

/* ===== UTILITY CLASSES ===== */
.text-gradient {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.bg-gradient-primary {
    background: var(--gradient-primary);
}

.bg-gradient-success {
    background: var(--gradient-success);
}

.bg-gradient-warning {
    background: var(--gradient-warning);
}

.bg-gradient-info {
    background: var(--gradient-info);
}

.shadow-hover {
    transition: var(--transition-base);
}

.shadow-hover:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.animate-fade-in {
    animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}
