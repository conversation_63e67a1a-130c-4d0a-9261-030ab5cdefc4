/**
 * Enhanced Modern CSS Framework
 * Comprehensive responsive styling for all page components
 * Mobile-first approach with advanced UI/UX patterns
 */

/* ===== CSS VARIABLES ===== */
:root {
    /* Colors */
    --primary: #0d6efd;
    --secondary: #6c757d;
    --success: #198754;
    --info: #0dcaf0;
    --warning: #ffc107;
    --danger: #dc3545;
    --light: #f8f9fa;
    --dark: #212529;
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-success: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    --gradient-warning: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --gradient-info: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 3rem;
    
    /* Border radius */
    --border-radius-sm: 0.375rem;
    --border-radius-md: 0.5rem;
    --border-radius-lg: 0.75rem;
    --border-radius-xl: 1rem;
    
    /* Shadows */
    --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --shadow-md: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
    
    /* Typography */
    --font-family-base: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    
    /* Transitions */
    --transition-base: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 0.15s ease-in-out;
}

/* ===== ENHANCED PAGE HEADER ===== */
.enhanced-page-header {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-bottom: 1px solid #e9ecef;
    padding: var(--spacing-lg) 0;
    margin-bottom: var(--spacing-lg);
    position: relative;
    overflow: hidden;
}

.enhanced-page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
}

.modern-breadcrumb {
    background: none;
    padding: 0;
    margin: 0;
    font-size: var(--font-size-sm);
}

.modern-breadcrumb .breadcrumb-item {
    color: var(--secondary);
}

.modern-breadcrumb .breadcrumb-link {
    color: var(--primary);
    text-decoration: none;
    transition: var(--transition-fast);
}

.modern-breadcrumb .breadcrumb-link:hover {
    color: var(--info);
    text-decoration: underline;
}

.page-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--dark);
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.title-icon {
    width: 3rem;
    height: 3rem;
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    background: rgba(13, 110, 253, 0.1);
}

.page-subtitle {
    color: var(--secondary);
    margin: var(--spacing-xs) 0 0 0;
    font-size: var(--font-size-lg);
    font-weight: 400;
}

.page-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
    justify-content: flex-end;
}

.action-btn {
    border-radius: var(--border-radius-md);
    font-weight: 500;
    padding: 0.5rem 1rem;
    transition: var(--transition-base);
    border: none;
    position: relative;
    overflow: hidden;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.action-btn:hover::before {
    left: 100%;
}

/* ===== ENHANCED STATS CARDS ===== */
.enhanced-stats-section {
    margin-bottom: var(--spacing-xl);
}

.enhanced-stats-card {
    background: white;
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid #e9ecef;
    transition: var(--transition-base);
    position: relative;
    overflow: hidden;
    height: 100%;
}

.enhanced-stats-card.gradient {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
}

.enhanced-stats-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.enhanced-stats-card.animate-card {
    opacity: 0;
    transform: translateY(20px);
    animation: slideInUp 0.6s ease-out forwards;
}

@keyframes slideInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.stats-card-body {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.stats-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-md);
}

.stats-icon-container {
    position: relative;
}

.stats-icon {
    width: 3.5rem;
    height: 3.5rem;
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    position: relative;
    overflow: hidden;
}

.stats-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.3));
    opacity: 0;
    transition: var(--transition-base);
}

.enhanced-stats-card:hover .stats-icon::before {
    opacity: 1;
}

.stats-trend {
    font-size: var(--font-size-sm);
    font-weight: 600;
}

.trend-indicator {
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius-sm);
    color: white;
}

.trend-up {
    background: var(--success);
}

.trend-down {
    background: var(--danger);
}

.stats-content {
    flex: 1;
}

.stats-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--dark);
    margin: 0 0 var(--spacing-xs) 0;
    line-height: 1.2;
}

.stats-label {
    font-size: var(--font-size-base);
    font-weight: 500;
    color: var(--dark);
    margin: 0;
}

.stats-subtitle {
    color: var(--secondary);
    font-size: var(--font-size-sm);
}

.stats-progress {
    margin-top: var(--spacing-md);
}

.stats-progress .progress {
    height: 0.5rem;
    border-radius: var(--border-radius-sm);
    background: #e9ecef;
}

.stats-progress .progress-bar {
    border-radius: var(--border-radius-sm);
    transition: width 1s ease-in-out;
}

/* ===== ENHANCED DATA TABLE ===== */
.enhanced-table-container {
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    border: 1px solid #e9ecef;
}

.table-controls {
    padding: var(--spacing-lg);
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.table-search .input-group-text {
    background: white;
    border-right: none;
    color: var(--secondary);
}

.table-search .form-control {
    border-left: none;
    box-shadow: none;
}

.table-search .form-control:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.enhanced-table {
    margin: 0;
    font-size: var(--font-size-sm);
}

.table-header th {
    background: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: var(--dark);
    padding: var(--spacing-md);
    position: relative;
}

.table-header th.sortable {
    cursor: pointer;
    user-select: none;
    transition: var(--transition-fast);
}

.table-header th.sortable:hover {
    background: #e9ecef;
}

.sort-icon {
    margin-left: var(--spacing-xs);
    opacity: 0.5;
    transition: var(--transition-fast);
}

.table-header th.sortable:hover .sort-icon {
    opacity: 1;
}

.enhanced-table tbody tr {
    transition: var(--transition-fast);
}

.enhanced-table tbody tr:hover {
    background: rgba(13, 110, 253, 0.05);
}

.enhanced-table td {
    padding: var(--spacing-md);
    vertical-align: middle;
    border-bottom: 1px solid #e9ecef;
}

.empty-state {
    padding: var(--spacing-xl);
}

.empty-state i {
    opacity: 0.5;
}

.action-buttons {
    display: flex;
    gap: var(--spacing-xs);
    justify-content: center;
}

.action-buttons .btn {
    padding: 0.375rem 0.5rem;
    border-radius: var(--border-radius-sm);
}

/* ===== ENHANCED FILTER ===== */
.enhanced-filter-container .card {
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
}

.enhanced-filter-container .card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
}

.filter-form .form-label {
    font-weight: 500;
    color: var(--dark);
    margin-bottom: var(--spacing-xs);
}

.filter-form .form-control,
.filter-form .form-select {
    border-radius: var(--border-radius-md);
    border: 1px solid #ced4da;
    transition: var(--transition-fast);
}

.filter-form .form-control:focus,
.filter-form .form-select:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.filter-actions {
    display: flex;
    gap: var(--spacing-sm);
    justify-content: flex-end;
    margin-top: var(--spacing-md);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .enhanced-page-header {
        padding: var(--spacing-md) 0;
    }
    
    .page-title {
        font-size: 1.5rem;
    }
    
    .title-icon {
        width: 2.5rem;
        height: 2.5rem;
        font-size: 1.25rem;
    }
    
    .page-actions {
        margin-top: var(--spacing-md);
        justify-content: flex-start;
    }
    
    .action-btn .btn-text {
        display: none;
    }
    
    .stats-value {
        font-size: 1.5rem;
    }
    
    .table-controls {
        padding: var(--spacing-md);
    }
    
    .table-controls .row {
        flex-direction: column;
        gap: var(--spacing-sm);
    }
    
    .table-info {
        text-align: left !important;
    }
    
    .filter-actions {
        justify-content: stretch;
    }
    
    .filter-actions .btn {
        flex: 1;
    }
}

@media (max-width: 576px) {
    .enhanced-stats-card {
        padding: var(--spacing-md);
    }
    
    .stats-icon {
        width: 3rem;
        height: 3rem;
        font-size: 1.25rem;
    }
    
    .stats-value {
        font-size: 1.25rem;
    }
    
    .enhanced-table {
        font-size: var(--font-size-xs);
    }
    
    .enhanced-table th,
    .enhanced-table td {
        padding: var(--spacing-sm);
    }
}

/* ===== UTILITY CLASSES ===== */
.text-gradient {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.bg-gradient-primary {
    background: var(--gradient-primary);
}

.bg-gradient-success {
    background: var(--gradient-success);
}

.bg-gradient-warning {
    background: var(--gradient-warning);
}

.bg-gradient-info {
    background: var(--gradient-info);
}

.shadow-hover {
    transition: var(--transition-base);
}

.shadow-hover:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.animate-fade-in {
    animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}
