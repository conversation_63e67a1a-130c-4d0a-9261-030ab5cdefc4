<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('/keuangan/login.php');
}

$currentUser = getCurrentUser();
$currentPage = 'investasi';

// Create investasi table if not exists
try {
    $pdo->exec("CREATE TABLE IF NOT EXISTS investasi (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        nama_investasi VARCHAR(255) NOT NULL,
        jenis_investasi ENUM('saham', 'obligasi', 'reksadana', 'emas', 'properti', 'deposito', 'crypto', 'lainnya') NOT NULL,
        jumlah_investasi DECIMAL(15,2) NOT NULL,
        nilai_saat_ini DECIMAL(15,2) DEFAULT 0,
        tanggal_investasi DATE NOT NULL,
        target_return DECIMAL(5,2) DEFAULT 0,
        periode_investasi INT DEFAULT 12,
        status ENUM('aktif', 'selesai', 'dijual') DEFAULT 'aktif',
        platform VARCHAR(100),
        keterangan TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_user_jenis (user_id, jenis_investasi),
        INDEX idx_status (status),
        INDEX idx_tanggal (tanggal_investasi)
    )");
} catch (PDOException $e) {
    error_log("Error creating investasi table: " . $e->getMessage());
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        switch ($_POST['action']) {
            case 'add':
                $errors = [];
                
                if (empty($_POST['nama_investasi'])) {
                    $errors[] = 'Nama investasi harus diisi';
                }
                if (empty($_POST['jumlah_investasi']) || $_POST['jumlah_investasi'] <= 0) {
                    $errors[] = 'Jumlah investasi harus lebih dari 0';
                }
                if (empty($_POST['tanggal_investasi'])) {
                    $errors[] = 'Tanggal investasi harus diisi';
                }
                
                if (empty($errors)) {
                    $stmt = $pdo->prepare("INSERT INTO investasi (user_id, nama_investasi, jenis_investasi, jumlah_investasi, nilai_saat_ini, tanggal_investasi, target_return, periode_investasi, platform, keterangan) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                    $result = $stmt->execute([
                        $currentUser['id'],
                        $_POST['nama_investasi'],
                        $_POST['jenis_investasi'],
                        $_POST['jumlah_investasi'],
                        $_POST['nilai_saat_ini'] ?? $_POST['jumlah_investasi'],
                        $_POST['tanggal_investasi'],
                        $_POST['target_return'] ?? 0,
                        $_POST['periode_investasi'] ?? 12,
                        $_POST['platform'] ?? '',
                        $_POST['keterangan'] ?? ''
                    ]);
                    
                    if ($result) {
                        setFlashMessage('success', 'Investasi berhasil ditambahkan');
                    } else {
                        setFlashMessage('danger', 'Gagal menambahkan investasi');
                    }
                } else {
                    setFlashMessage('danger', implode('<br>', $errors));
                }
                break;
                
            case 'update':
                if (!empty($_POST['id'])) {
                    $stmt = $pdo->prepare("UPDATE investasi SET nama_investasi = ?, jenis_investasi = ?, jumlah_investasi = ?, nilai_saat_ini = ?, tanggal_investasi = ?, target_return = ?, periode_investasi = ?, platform = ?, keterangan = ?, status = ? WHERE id = ? AND user_id = ?");
                    $result = $stmt->execute([
                        $_POST['nama_investasi'],
                        $_POST['jenis_investasi'],
                        $_POST['jumlah_investasi'],
                        $_POST['nilai_saat_ini'],
                        $_POST['tanggal_investasi'],
                        $_POST['target_return'],
                        $_POST['periode_investasi'],
                        $_POST['platform'],
                        $_POST['keterangan'],
                        $_POST['status'],
                        $_POST['id'],
                        $currentUser['id']
                    ]);
                    
                    if ($result) {
                        setFlashMessage('success', 'Investasi berhasil diperbarui');
                    } else {
                        setFlashMessage('danger', 'Gagal memperbarui investasi');
                    }
                }
                break;
                
            case 'delete':
                if (!empty($_POST['id'])) {
                    $stmt = $pdo->prepare("DELETE FROM investasi WHERE id = ? AND user_id = ?");
                    $result = $stmt->execute([$_POST['id'], $currentUser['id']]);
                    
                    if ($result) {
                        setFlashMessage('success', 'Investasi berhasil dihapus');
                    } else {
                        setFlashMessage('danger', 'Gagal menghapus investasi');
                    }
                }
                break;
        }
        
        redirect('/keuangan/investasi.php');
        
    } catch (PDOException $e) {
        error_log("Investasi error: " . $e->getMessage());
        setFlashMessage('danger', 'Terjadi kesalahan database. Silakan coba lagi.');
    } catch (Exception $e) {
        error_log("General error in investasi: " . $e->getMessage());
        setFlashMessage('danger', 'Terjadi kesalahan. Silakan coba lagi.');
    }
}

// Get statistics
try {
    $stmt = $pdo->prepare("
        SELECT 
            COUNT(*) as total,
            COUNT(CASE WHEN status = 'aktif' THEN 1 END) as aktif,
            COUNT(CASE WHEN status = 'selesai' THEN 1 END) as selesai,
            COUNT(CASE WHEN status = 'dijual' THEN 1 END) as dijual,
            SUM(jumlah_investasi) as total_investasi,
            SUM(nilai_saat_ini) as total_nilai_sekarang,
            AVG(target_return) as avg_target_return
        FROM investasi 
        WHERE user_id = ?
    ");
    $stmt->execute([$currentUser['id']]);
    $stats = $stmt->fetch();
    
    // Calculate total return
    $total_return = 0;
    $return_percentage = 0;
    if ($stats['total_investasi'] > 0) {
        $total_return = $stats['total_nilai_sekarang'] - $stats['total_investasi'];
        $return_percentage = ($total_return / $stats['total_investasi']) * 100;
    }
} catch (PDOException $e) {
    $stats = [
        'total' => 0,
        'aktif' => 0,
        'selesai' => 0,
        'dijual' => 0,
        'total_investasi' => 0,
        'total_nilai_sekarang' => 0,
        'avg_target_return' => 0
    ];
    $total_return = 0;
    $return_percentage = 0;
}

// Get filter parameters
$where = ["user_id = ?"];
$params = [$currentUser['id']];

if (!empty($_GET['jenis'])) {
    $where[] = "jenis_investasi = ?";
    $params[] = $_GET['jenis'];
}

if (!empty($_GET['status'])) {
    $where[] = "status = ?";
    $params[] = $_GET['status'];
}

if (!empty($_GET['search'])) {
    $where[] = "(nama_investasi LIKE ? OR platform LIKE ? OR keterangan LIKE ?)";
    $searchTerm = '%' . $_GET['search'] . '%';
    $params[] = $searchTerm;
    $params[] = $searchTerm;
    $params[] = $searchTerm;
}

// Get investasi data
try {
    $whereClause = implode(' AND ', $where);
    $stmt = $pdo->prepare("SELECT * FROM investasi WHERE $whereClause ORDER BY tanggal_investasi DESC");
    $stmt->execute($params);
    $investasi = $stmt->fetchAll();
} catch (PDOException $e) {
    $investasi = [];
    error_log("Error fetching investasi: " . $e->getMessage());
}

// Get investasi by type for chart
try {
    $stmt = $pdo->prepare("
        SELECT 
            jenis_investasi,
            COUNT(*) as jumlah,
            SUM(jumlah_investasi) as total_investasi,
            SUM(nilai_saat_ini) as total_nilai
        FROM investasi 
        WHERE user_id = ? AND status = 'aktif'
        GROUP BY jenis_investasi
        ORDER BY total_investasi DESC
    ");
    $stmt->execute([$currentUser['id']]);
    $investasiByType = $stmt->fetchAll();
} catch (PDOException $e) {
    $investasiByType = [];
}

// Helper functions
function getInvestmentIcon($jenis) {
    $icons = [
        'saham' => 'chart-line',
        'obligasi' => 'certificate',
        'reksadana' => 'coins',
        'emas' => 'gem',
        'properti' => 'home',
        'deposito' => 'piggy-bank',
        'crypto' => 'bitcoin',
        'lainnya' => 'dollar-sign'
    ];
    return $icons[$jenis] ?? 'dollar-sign';
}

function getStatusColor($status) {
    $colors = [
        'aktif' => 'success',
        'selesai' => 'primary',
        'dijual' => 'warning'
    ];
    return $colors[$status] ?? 'secondary';
}

function getStatusIcon($status) {
    $icons = [
        'aktif' => 'play',
        'selesai' => 'check',
        'dijual' => 'exchange-alt'
    ];
    return $icons[$status] ?? 'question';
}

require_once 'includes/views/layouts/header.php';
require_once 'includes/views/layouts/sidebar.php';
?>

<div class="main-content">
    <div class="container-fluid">
        <!-- Flash Messages -->
        <?php if (hasFlashMessage()): ?>
            <div class="alert alert-<?= getFlashMessage()['type'] ?> alert-dismissible fade show">
                <?= getFlashMessage()['message'] ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-1">
                    <i class="fas fa-chart-line text-primary me-2"></i>Portfolio Investasi
                </h1>
                <p class="text-muted mb-0">Kelola dan pantau investasi Anda</p>
            </div>
            <div class="d-flex gap-2">
                <button type="button" class="btn btn-success" onclick="exportInvestasi()">
                    <i class="fas fa-download me-2"></i>Export
                </button>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addInvestasiModal">
                    <i class="fas fa-plus me-2"></i>Tambah Investasi
                </button>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row g-3 mb-4">
            <div class="col-lg-3 col-md-6">
                <div class="card border-0 shadow-sm h-100 bg-gradient-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1 opacity-75">Total Investasi</h6>
                                <h2 class="mb-0 fw-bold"><?= formatRupiah($stats['total_investasi'] ?? 0) ?></h2>
                                <small class="opacity-75"><?= $stats['total'] ?? 0 ?> investasi</small>
                            </div>
                            <div class="bg-white bg-opacity-20 p-3 rounded-circle">
                                <i class="fas fa-wallet fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="card border-0 shadow-sm h-100 bg-gradient-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1 opacity-75">Nilai Sekarang</h6>
                                <h2 class="mb-0 fw-bold"><?= formatRupiah($stats['total_nilai_sekarang'] ?? 0) ?></h2>
                                <small class="opacity-75"><?= $stats['aktif'] ?? 0 ?> aktif</small>
                            </div>
                            <div class="bg-white bg-opacity-20 p-3 rounded-circle">
                                <i class="fas fa-chart-line fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="card border-0 shadow-sm h-100 bg-gradient-<?= $total_return >= 0 ? 'success' : 'danger' ?> text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1 opacity-75">Total Return</h6>
                                <h2 class="mb-0 fw-bold"><?= formatRupiah($total_return) ?></h2>
                                <small class="opacity-75"><?= number_format($return_percentage, 2) ?>%</small>
                            </div>
                            <div class="bg-white bg-opacity-20 p-3 rounded-circle">
                                <i class="fas fa-<?= $total_return >= 0 ? 'arrow-up' : 'arrow-down' ?> fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="card border-0 shadow-sm h-100 bg-gradient-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1 opacity-75">Target Return</h6>
                                <h2 class="mb-0 fw-bold"><?= number_format($stats['avg_target_return'] ?? 0, 1) ?>%</h2>
                                <small class="opacity-75">Rata-rata</small>
                            </div>
                            <div class="bg-white bg-opacity-20 p-3 rounded-circle">
                                <i class="fas fa-bullseye fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Portfolio Overview -->
        <div class="row mb-4">
            <div class="col-lg-8">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white border-0">
                        <h6 class="mb-0"><i class="fas fa-pie-chart me-2"></i>Distribusi Portfolio</h6>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($investasiByType)): ?>
                        <canvas id="portfolioChart" height="300"></canvas>
                        <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fas fa-chart-pie fa-3x text-muted mb-3"></i>
                            <p class="text-muted">Belum ada data investasi untuk ditampilkan</p>
                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addInvestasiModal">
                                <i class="fas fa-plus me-2"></i>Tambah Investasi Pertama
                            </button>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white border-0">
                        <h6 class="mb-0"><i class="fas fa-trophy me-2"></i>Top Performing</h6>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($investasiByType)): ?>
                        <?php foreach (array_slice($investasiByType, 0, 5) as $index => $type): ?>
                        <?php
                        $return_pct = 0;
                        if ($type['total_investasi'] > 0) {
                            $return_pct = (($type['total_nilai'] - $type['total_investasi']) / $type['total_investasi']) * 100;
                        }
                        ?>
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <div class="fw-bold"><?= ucfirst($type['jenis_investasi']) ?></div>
                                <small class="text-muted"><?= $type['jumlah'] ?> investasi</small>
                            </div>
                            <div class="text-end">
                                <div class="fw-bold text-<?= $return_pct >= 0 ? 'success' : 'danger' ?>">
                                    <?= number_format($return_pct, 2) ?>%
                                </div>
                                <small class="text-muted"><?= formatRupiah($type['total_nilai']) ?></small>
                            </div>
                        </div>
                        <?php endforeach; ?>
                        <?php else: ?>
                        <div class="text-center py-3">
                            <i class="fas fa-chart-bar fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-0">Belum ada data</p>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-body">
                        <div class="row g-2">
                            <div class="col-md-2">
                                <a href="?jenis=saham" class="btn btn-outline-primary w-100">
                                    <i class="fas fa-chart-line me-2"></i>Saham
                                </a>
                            </div>
                            <div class="col-md-2">
                                <a href="?jenis=reksadana" class="btn btn-outline-success w-100">
                                    <i class="fas fa-coins me-2"></i>Reksadana
                                </a>
                            </div>
                            <div class="col-md-2">
                                <a href="?jenis=emas" class="btn btn-outline-warning w-100">
                                    <i class="fas fa-gem me-2"></i>Emas
                                </a>
                            </div>
                            <div class="col-md-2">
                                <a href="?jenis=crypto" class="btn btn-outline-info w-100">
                                    <i class="fab fa-bitcoin me-2"></i>Crypto
                                </a>
                            </div>
                            <div class="col-md-2">
                                <a href="?status=aktif" class="btn btn-outline-success w-100">
                                    <i class="fas fa-play me-2"></i>Aktif
                                </a>
                            </div>
                            <div class="col-md-2">
                                <a href="/keuangan/investasi.php" class="btn btn-outline-dark w-100">
                                    <i class="fas fa-sync me-2"></i>Semua
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filter Section -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-light border-0">
                <h6 class="mb-0"><i class="fas fa-filter me-2"></i>Filter & Pencarian</h6>
            </div>
            <div class="card-body">
                <form action="" method="GET" class="row g-3">
                    <div class="col-md-4">
                        <label class="form-label">Pencarian</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-search"></i></span>
                            <input type="text" name="search" class="form-control" placeholder="Cari nama investasi, platform..." value="<?= $_GET['search'] ?? '' ?>">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Jenis Investasi</label>
                        <select name="jenis" class="form-select">
                            <option value="">Semua Jenis</option>
                            <option value="saham" <?= (isset($_GET['jenis']) && $_GET['jenis'] == 'saham') ? 'selected' : '' ?>>Saham</option>
                            <option value="obligasi" <?= (isset($_GET['jenis']) && $_GET['jenis'] == 'obligasi') ? 'selected' : '' ?>>Obligasi</option>
                            <option value="reksadana" <?= (isset($_GET['jenis']) && $_GET['jenis'] == 'reksadana') ? 'selected' : '' ?>>Reksadana</option>
                            <option value="emas" <?= (isset($_GET['jenis']) && $_GET['jenis'] == 'emas') ? 'selected' : '' ?>>Emas</option>
                            <option value="properti" <?= (isset($_GET['jenis']) && $_GET['jenis'] == 'properti') ? 'selected' : '' ?>>Properti</option>
                            <option value="deposito" <?= (isset($_GET['jenis']) && $_GET['jenis'] == 'deposito') ? 'selected' : '' ?>>Deposito</option>
                            <option value="crypto" <?= (isset($_GET['jenis']) && $_GET['jenis'] == 'crypto') ? 'selected' : '' ?>>Cryptocurrency</option>
                            <option value="lainnya" <?= (isset($_GET['jenis']) && $_GET['jenis'] == 'lainnya') ? 'selected' : '' ?>>Lainnya</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Status</label>
                        <select name="status" class="form-select">
                            <option value="">Semua Status</option>
                            <option value="aktif" <?= (isset($_GET['status']) && $_GET['status'] == 'aktif') ? 'selected' : '' ?>>Aktif</option>
                            <option value="selesai" <?= (isset($_GET['status']) && $_GET['status'] == 'selesai') ? 'selected' : '' ?>>Selesai</option>
                            <option value="dijual" <?= (isset($_GET['status']) && $_GET['status'] == 'dijual') ? 'selected' : '' ?>>Dijual</option>
                        </select>
                    </div>
                    <div class="col-md-2 d-flex align-items-end">
                        <div class="btn-group w-100">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-2"></i>Cari
                            </button>
                            <a href="/keuangan/investasi.php" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i>
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Investasi Table -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-0 d-flex justify-content-between align-items-center">
                <h6 class="mb-0"><i class="fas fa-list me-2"></i>Daftar Investasi</h6>
                <div class="d-flex gap-2">
                    <div class="btn-group btn-group-sm">
                        <button type="button" class="btn btn-outline-secondary active" onclick="toggleView('table')">
                            <i class="fas fa-table"></i>
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="toggleView('grid')">
                            <i class="fas fa-th"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                <div id="tableView">
                    <div class="table-responsive">
                        <table class="table table-hover align-middle mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th class="ps-4">Investasi</th>
                                    <th>Jenis</th>
                                    <th>Jumlah Investasi</th>
                                    <th>Nilai Sekarang</th>
                                    <th>Return</th>
                                    <th>Platform</th>
                                    <th class="text-center">Status</th>
                                    <th class="text-center">Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($investasi)): ?>
                                <tr>
                                    <td colspan="8" class="text-center py-5">
                                        <div class="text-muted">
                                            <i class="fas fa-chart-line fa-3x mb-3 opacity-50"></i>
                                            <p class="mb-0">Belum ada data investasi</p>
                                            <small>Tambahkan investasi pertama Anda</small>
                                        </div>
                                    </td>
                                </tr>
                                <?php else: ?>
                                <?php foreach ($investasi as $inv): ?>
                                <?php
                                $return_amount = $inv['nilai_saat_ini'] - $inv['jumlah_investasi'];
                                $return_pct = $inv['jumlah_investasi'] > 0 ? ($return_amount / $inv['jumlah_investasi']) * 100 : 0;
                                $days_invested = (new DateTime())->diff(new DateTime($inv['tanggal_investasi']))->days;
                                ?>
                                <tr>
                                    <td class="ps-4">
                                        <div class="d-flex align-items-center">
                                            <div class="bg-primary bg-opacity-10 p-2 rounded me-3">
                                                <i class="fas fa-<?= getInvestmentIcon($inv['jenis_investasi']) ?> text-primary"></i>
                                            </div>
                                            <div>
                                                <div class="fw-bold"><?= htmlspecialchars($inv['nama_investasi']) ?></div>
                                                <small class="text-muted">
                                                    Investasi sejak <?= formatTanggal($inv['tanggal_investasi']) ?>
                                                    (<?= $days_invested ?> hari)
                                                </small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary bg-opacity-10 text-dark">
                                            <?= ucfirst($inv['jenis_investasi']) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="fw-bold"><?= formatRupiah($inv['jumlah_investasi']) ?></div>
                                        <?php if ($inv['target_return'] > 0): ?>
                                            <small class="text-muted">Target: <?= $inv['target_return'] ?>%</small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="fw-bold"><?= formatRupiah($inv['nilai_saat_ini']) ?></div>
                                    </td>
                                    <td>
                                        <div class="fw-bold text-<?= $return_amount >= 0 ? 'success' : 'danger' ?>">
                                            <?= formatRupiah($return_amount) ?>
                                        </div>
                                        <small class="text-<?= $return_pct >= 0 ? 'success' : 'danger' ?>">
                                            <?= number_format($return_pct, 2) ?>%
                                        </small>
                                    </td>
                                    <td>
                                        <?php if ($inv['platform']): ?>
                                            <span class="badge bg-info bg-opacity-10 text-info">
                                                <?= htmlspecialchars($inv['platform']) ?>
                                            </span>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="text-center">
                                        <span class="badge bg-<?= getStatusColor($inv['status']) ?> px-3 py-2">
                                            <i class="fas fa-<?= getStatusIcon($inv['status']) ?> me-1"></i>
                                            <?= ucfirst($inv['status']) ?>
                                        </span>
                                    </td>
                                    <td class="text-center">
                                        <div class="btn-group btn-group-sm">
                                            <button type="button" class="btn btn-outline-info" onclick="viewInvestasi(<?= htmlspecialchars(json_encode($inv)) ?>)" title="Lihat Detail">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-primary" onclick="editInvestasi(<?= htmlspecialchars(json_encode($inv)) ?>)" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-danger" onclick="deleteInvestasi(<?= $inv['id'] ?>)" title="Hapus">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Grid View (Hidden by default) -->
                <div id="gridView" style="display: none;">
                    <div class="row g-3 p-3">
                        <?php foreach ($investasi as $inv): ?>
                        <?php
                        $return_amount = $inv['nilai_saat_ini'] - $inv['jumlah_investasi'];
                        $return_pct = $inv['jumlah_investasi'] > 0 ? ($return_amount / $inv['jumlah_investasi']) * 100 : 0;
                        ?>
                        <div class="col-lg-4 col-md-6">
                            <div class="card h-100 border-0 shadow-sm">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start mb-3">
                                        <div class="bg-primary bg-opacity-10 p-2 rounded">
                                            <i class="fas fa-<?= getInvestmentIcon($inv['jenis_investasi']) ?> text-primary"></i>
                                        </div>
                                        <span class="badge bg-<?= getStatusColor($inv['status']) ?>">
                                            <?= ucfirst($inv['status']) ?>
                                        </span>
                                    </div>
                                    <h6 class="card-title"><?= htmlspecialchars($inv['nama_investasi']) ?></h6>
                                    <div class="card-text">
                                        <div class="row text-center mb-3">
                                            <div class="col-6">
                                                <div class="fw-bold"><?= formatRupiah($inv['jumlah_investasi']) ?></div>
                                                <small class="text-muted">Investasi</small>
                                            </div>
                                            <div class="col-6">
                                                <div class="fw-bold text-<?= $return_amount >= 0 ? 'success' : 'danger' ?>">
                                                    <?= number_format($return_pct, 2) ?>%
                                                </div>
                                                <small class="text-muted">Return</small>
                                            </div>
                                        </div>
                                        <small class="text-muted d-block"><i class="fas fa-calendar me-1"></i><?= formatTanggal($inv['tanggal_investasi']) ?></small>
                                        <?php if ($inv['platform']): ?>
                                            <small class="text-muted d-block"><i class="fas fa-building me-1"></i><?= htmlspecialchars($inv['platform']) ?></small>
                                        <?php endif; ?>
                                    </div>
                                    <div class="btn-group w-100 mt-3">
                                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="editInvestasi(<?= htmlspecialchars(json_encode($inv)) ?>)">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-info" onclick="viewInvestasi(<?= htmlspecialchars(json_encode($inv)) ?>)">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteInvestasi(<?= $inv['id'] ?>)">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Investasi Modal -->
<div class="modal fade" id="addInvestasiModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header border-0">
                <h5 class="modal-title">Tambah Investasi Baru</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <input type="hidden" name="action" value="add">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Nama Investasi <span class="text-danger">*</span></label>
                                <input type="text" name="nama_investasi" class="form-control" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Jenis Investasi <span class="text-danger">*</span></label>
                                <select name="jenis_investasi" class="form-select" required>
                                    <option value="">Pilih Jenis</option>
                                    <option value="saham">Saham</option>
                                    <option value="obligasi">Obligasi</option>
                                    <option value="reksadana">Reksadana</option>
                                    <option value="emas">Emas</option>
                                    <option value="properti">Properti</option>
                                    <option value="deposito">Deposito</option>
                                    <option value="crypto">Cryptocurrency</option>
                                    <option value="lainnya">Lainnya</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Jumlah Investasi <span class="text-danger">*</span></label>
                                <input type="number" name="jumlah_investasi" class="form-control" step="0.01" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Nilai Saat Ini</label>
                                <input type="number" name="nilai_saat_ini" class="form-control" step="0.01">
                                <small class="text-muted">Kosongkan jika sama dengan jumlah investasi</small>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Tanggal Investasi <span class="text-danger">*</span></label>
                                <input type="date" name="tanggal_investasi" class="form-control" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Target Return (%)</label>
                                <input type="number" name="target_return" class="form-control" step="0.01" min="0" max="100">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Periode Investasi (bulan)</label>
                                <input type="number" name="periode_investasi" class="form-control" min="1" value="12">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Platform/Broker</label>
                                <input type="text" name="platform" class="form-control" placeholder="Contoh: BCA Sekuritas, Bibit, dll">
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Keterangan</label>
                        <textarea name="keterangan" class="form-control" rows="3" placeholder="Catatan tambahan tentang investasi ini..."></textarea>
                    </div>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Simpan Investasi</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Investasi Modal -->
<div class="modal fade" id="editInvestasiModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header border-0">
                <h5 class="modal-title">Edit Investasi</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <input type="hidden" name="action" value="update">
                <input type="hidden" name="id" id="edit_id">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Nama Investasi <span class="text-danger">*</span></label>
                                <input type="text" name="nama_investasi" id="edit_nama_investasi" class="form-control" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Jenis Investasi <span class="text-danger">*</span></label>
                                <select name="jenis_investasi" id="edit_jenis_investasi" class="form-select" required>
                                    <option value="">Pilih Jenis</option>
                                    <option value="saham">Saham</option>
                                    <option value="obligasi">Obligasi</option>
                                    <option value="reksadana">Reksadana</option>
                                    <option value="emas">Emas</option>
                                    <option value="properti">Properti</option>
                                    <option value="deposito">Deposito</option>
                                    <option value="crypto">Cryptocurrency</option>
                                    <option value="lainnya">Lainnya</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Jumlah Investasi <span class="text-danger">*</span></label>
                                <input type="number" name="jumlah_investasi" id="edit_jumlah_investasi" class="form-control" step="0.01" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Nilai Saat Ini</label>
                                <input type="number" name="nilai_saat_ini" id="edit_nilai_saat_ini" class="form-control" step="0.01">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Tanggal Investasi <span class="text-danger">*</span></label>
                                <input type="date" name="tanggal_investasi" id="edit_tanggal_investasi" class="form-control" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Target Return (%)</label>
                                <input type="number" name="target_return" id="edit_target_return" class="form-control" step="0.01" min="0" max="100">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Periode Investasi (bulan)</label>
                                <input type="number" name="periode_investasi" id="edit_periode_investasi" class="form-control" min="1">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Platform/Broker</label>
                                <input type="text" name="platform" id="edit_platform" class="form-control">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label class="form-label">Status</label>
                                <select name="status" id="edit_status" class="form-select">
                                    <option value="aktif">Aktif</option>
                                    <option value="selesai">Selesai</option>
                                    <option value="dijual">Dijual</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Keterangan</label>
                        <textarea name="keterangan" id="edit_keterangan" class="form-control" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Update Investasi</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
let currentInvestasiData = null;

function editInvestasi(investasi) {
    currentInvestasiData = investasi;

    document.getElementById('edit_id').value = investasi.id;
    document.getElementById('edit_nama_investasi').value = investasi.nama_investasi;
    document.getElementById('edit_jenis_investasi').value = investasi.jenis_investasi;
    document.getElementById('edit_jumlah_investasi').value = investasi.jumlah_investasi;
    document.getElementById('edit_nilai_saat_ini').value = investasi.nilai_saat_ini;
    document.getElementById('edit_tanggal_investasi').value = investasi.tanggal_investasi;
    document.getElementById('edit_target_return').value = investasi.target_return;
    document.getElementById('edit_periode_investasi').value = investasi.periode_investasi;
    document.getElementById('edit_platform').value = investasi.platform || '';
    document.getElementById('edit_status').value = investasi.status;
    document.getElementById('edit_keterangan').value = investasi.keterangan || '';

    const modal = new bootstrap.Modal(document.getElementById('editInvestasiModal'));
    modal.show();
}

function deleteInvestasi(id) {
    if (confirm('⚠️ Apakah Anda yakin ingin menghapus investasi ini?\n\nData yang sudah dihapus tidak dapat dikembalikan.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '/keuangan/investasi.php';

        const actionInput = document.createElement('input');
        actionInput.type = 'hidden';
        actionInput.name = 'action';
        actionInput.value = 'delete';

        const idInput = document.createElement('input');
        idInput.type = 'hidden';
        idInput.name = 'id';
        idInput.value = id;

        form.appendChild(actionInput);
        form.appendChild(idInput);
        document.body.appendChild(form);
        form.submit();
    }
}

function viewInvestasi(investasi) {
    alert('Detail Investasi:\n\n' +
          'Nama: ' + investasi.nama_investasi + '\n' +
          'Jenis: ' + investasi.jenis_investasi + '\n' +
          'Jumlah: Rp ' + new Intl.NumberFormat('id-ID').format(investasi.jumlah_investasi) + '\n' +
          'Nilai Sekarang: Rp ' + new Intl.NumberFormat('id-ID').format(investasi.nilai_saat_ini) + '\n' +
          'Platform: ' + (investasi.platform || '-') + '\n' +
          'Status: ' + investasi.status);
}

function toggleView(view) {
    const tableView = document.getElementById('tableView');
    const gridView = document.getElementById('gridView');
    const buttons = document.querySelectorAll('.btn-group .btn');

    buttons.forEach(btn => btn.classList.remove('active'));

    if (view === 'table') {
        tableView.style.display = 'block';
        gridView.style.display = 'none';
        buttons[0].classList.add('active');
    } else {
        tableView.style.display = 'none';
        gridView.style.display = 'block';
        buttons[1].classList.add('active');
    }
}

function exportInvestasi() {
    alert('Fitur export akan segera tersedia');
}

// Portfolio Chart
<?php if (!empty($investasiByType)): ?>
document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('portfolioChart').getContext('2d');

    const data = {
        labels: [<?php foreach ($investasiByType as $type): ?>'<?= ucfirst($type['jenis_investasi']) ?>',<?php endforeach; ?>],
        datasets: [{
            data: [<?php foreach ($investasiByType as $type): ?><?= $type['total_nilai'] ?>,<?php endforeach; ?>],
            backgroundColor: [
                '#007bff', '#28a745', '#ffc107', '#dc3545', '#6f42c1', '#fd7e14', '#20c997', '#6c757d'
            ],
            borderWidth: 0
        }]
    };

    new Chart(ctx, {
        type: 'doughnut',
        data: data,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const value = new Intl.NumberFormat('id-ID', {
                                style: 'currency',
                                currency: 'IDR',
                                minimumFractionDigits: 0
                            }).format(context.raw);
                            return context.label + ': ' + value;
                        }
                    }
                }
            }
        }
    });
});
<?php endif; ?>
</script>

<style>
.bg-gradient-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}

.bg-gradient-success {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
}

.bg-gradient-danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
}

.bg-gradient-info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
}

.text-truncate {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.table-responsive {
    border-radius: 0.375rem;
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

@media (max-width: 768px) {
    .card-body .row .col-md-2 {
        margin-bottom: 0.5rem;
    }

    .btn-group {
        flex-direction: column;
    }

    .btn-group .btn {
        border-radius: 0.375rem !important;
        margin-bottom: 0.25rem;
    }

    .d-flex.gap-2 {
        flex-direction: column;
        gap: 0.5rem !important;
    }
}

.card-header {
    border-bottom: 1px solid rgba(0,0,0,.125);
}

.badge.fs-6 {
    font-size: 0.875rem !important;
    padding: 0.5rem 1rem;
}

#portfolioChart {
    max-height: 300px;
}
</style>

<?php require_once 'includes/views/layouts/footer.php'; ?>
