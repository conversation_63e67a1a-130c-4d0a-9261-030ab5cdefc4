<?php
/**
 * <PERSON><PERSON>t untuk mengupdate semua halaman sidebar dengan template modern
 * Dengan icon yang tepat dan konsisten
 */

// Daftar semua halaman sidebar dengan konfigurasi lengkap
$sidebarPages = [
    'produk.php' => [
        'title' => 'Katalog Produk & Layanan',
        'subtitle' => 'Kelola produk, layanan, dan inventory bisnis Anda dengan sistem yang terintegrasi',
        'icon' => 'fas fa-box-open',
        'breadcrumbs' => [
            ['label' => 'Home', 'url' => '/keuangan/dashboard.php', 'icon' => 'fas fa-home'],
            ['label' => 'Produk & Layanan', 'url' => '/keuangan/produk.php', 'icon' => 'fas fa-box-open']
        ],
        'actions' => [
            [
                'type' => 'button',
                'label' => 'Tambah Produk',
                'icon' => 'fas fa-plus',
                'class' => 'btn-primary',
                'data-bs-toggle' => 'modal',
                'data-bs-target' => '#addProductModal'
            ]
        ]
    ],
    'supplier.php' => [
        'title' => 'Direktori Supplier & Vendor',
        'subtitle' => 'Kelola data supplier, vendor, dan mitra bisnis dengan sistem kontak terintegrasi',
        'icon' => 'fas fa-truck-loading',
        'breadcrumbs' => [
            ['label' => 'Home', 'url' => '/keuangan/dashboard.php', 'icon' => 'fas fa-home'],
            ['label' => 'Supplier & Vendor', 'url' => '/keuangan/supplier.php', 'icon' => 'fas fa-truck-loading']
        ],
        'actions' => [
            [
                'type' => 'button',
                'label' => 'Tambah Supplier',
                'icon' => 'fas fa-plus',
                'class' => 'btn-primary',
                'data-bs-toggle' => 'modal',
                'data-bs-target' => '#addSupplierModal'
            ]
        ]
    ],
    'inventory.php' => [
        'title' => 'Manajemen Inventory & Stok',
        'subtitle' => 'Pantau stok barang, kelola inventory, dan tracking pergerakan barang secara real-time',
        'icon' => 'fas fa-warehouse',
        'breadcrumbs' => [
            ['label' => 'Home', 'url' => '/keuangan/dashboard.php', 'icon' => 'fas fa-home'],
            ['label' => 'Inventory & Stok', 'url' => '/keuangan/inventory.php', 'icon' => 'fas fa-warehouse']
        ],
        'actions' => [
            [
                'type' => 'button',
                'label' => 'Tambah Item',
                'icon' => 'fas fa-plus',
                'class' => 'btn-primary',
                'data-bs-toggle' => 'modal',
                'data-bs-target' => '#addInventoryModal'
            ]
        ]
    ],
    'return.php' => [
        'title' => 'Manajemen Return & Retur',
        'subtitle' => 'Kelola return barang, retur penjualan, dan proses pengembalian dengan tracking lengkap',
        'icon' => 'fas fa-undo-alt',
        'breadcrumbs' => [
            ['label' => 'Home', 'url' => '/keuangan/dashboard.php', 'icon' => 'fas fa-home'],
            ['label' => 'Return & Retur', 'url' => '/keuangan/return.php', 'icon' => 'fas fa-undo-alt']
        ],
        'actions' => [
            [
                'type' => 'button',
                'label' => 'Proses Return',
                'icon' => 'fas fa-plus',
                'class' => 'btn-primary',
                'data-bs-toggle' => 'modal',
                'data-bs-target' => '#addReturnModal'
            ]
        ]
    ],
    'laporan.php' => [
        'title' => 'Laporan & Analisis Keuangan',
        'subtitle' => 'Dashboard analisis keuangan dengan laporan komprehensif dan visualisasi data interaktif',
        'icon' => 'fas fa-chart-line',
        'breadcrumbs' => [
            ['label' => 'Home', 'url' => '/keuangan/dashboard.php', 'icon' => 'fas fa-home'],
            ['label' => 'Laporan & Analisis', 'url' => '/keuangan/laporan.php', 'icon' => 'fas fa-chart-line']
        ],
        'actions' => [
            [
                'type' => 'button',
                'label' => 'Generate Laporan',
                'icon' => 'fas fa-file-export',
                'class' => 'btn-primary',
                'data-bs-toggle' => 'modal',
                'data-bs-target' => '#generateReportModal'
            ]
        ]
    ]
];

function updatePageWithModernTemplate($filename, $config) {
    if (!file_exists($filename)) {
        echo "❌ File $filename tidak ditemukan.\n";
        return false;
    }
    
    $content = file_get_contents($filename);
    
    // 1. Update header include dan page header
    $headerPatterns = [
        '/include \'includes\/views\/layouts\/header\.php\';.*?<div class="container[^>]*>.*?<\/div>/s',
        '/include \'includes\/views\/layouts\/header\.php\';.*?<!-- Page Header -->.*?<\/div>/s',
        '/include \'includes\/views\/layouts\/header\.php\';.*?<h1[^>]*>.*?<\/div>/s'
    ];
    
    $breadcrumbsJson = json_encode($config['breadcrumbs'], JSON_UNESCAPED_SLASHES);
    $actionsJson = json_encode($config['actions'], JSON_UNESCAPED_SLASHES);
    
    $newHeader = "require_once 'includes/views/layouts/modern_page_template.php';
include 'includes/views/layouts/header.php';
?>

<div class=\"content-wrapper\">
    <div class=\"container-fluid\">
        <div class=\"row justify-content-center\">
            <div class=\"col-12 col-xl-11 col-xxl-10\">
                <div class=\"main-content-container\">
                    
                    <?php
                    // Page Header
                    renderModernPageHeader(
                        '{$config['title']}',
                        '{$config['subtitle']}',
                        $breadcrumbsJson,
                        $actionsJson
                    );
                    ?>";
    
    $headerUpdated = false;
    foreach ($headerPatterns as $pattern) {
        $newContent = preg_replace($pattern, $newHeader, $content);
        if ($newContent !== $content) {
            $content = $newContent;
            $headerUpdated = true;
            break;
        }
    }
    
    // 2. Update footer structure
    $footerPatterns = [
        '/<\/div>\s*<\/div>\s*(?=<\?php include.*footer|$)/s',
        '/<\/div>\s*(?=<\?php include.*footer|$)/s'
    ];
    
    $newFooter = "                </div>
            </div>
        </div>
    </div>
</div>";
    
    $footerUpdated = false;
    foreach ($footerPatterns as $pattern) {
        $newContent = preg_replace($pattern, $newFooter, $content);
        if ($newContent !== $content) {
            $content = $newContent;
            $footerUpdated = true;
            break;
        }
    }
    
    // 3. Add modern CSS if not exists
    if (strpos($content, 'modern-pages.css') === false) {
        $cssInclude = '<link href="assets/css/modern-pages.css?v=' . time() . '" rel="stylesheet">';
        $headerIncludePos = strpos($content, "include 'includes/views/layouts/header.php';");
        
        if ($headerIncludePos !== false) {
            $insertPos = strpos($content, '?>', $headerIncludePos) + 2;
            $content = substr_replace($content, "\n" . $cssInclude . "\n", $insertPos, 0);
        }
    }
    
    // Save updated content
    file_put_contents($filename, $content);
    
    echo "✅ $filename berhasil diupdate:\n";
    echo "   📝 Title: {$config['title']}\n";
    echo "   🎨 Icon: {$config['icon']}\n";
    echo "   🔄 Header: " . ($headerUpdated ? "Updated" : "No change") . "\n";
    echo "   🔄 Footer: " . ($footerUpdated ? "Updated" : "No change") . "\n\n";
    
    return true;
}

// Jalankan update untuk semua halaman
echo "🚀 Memulai modernisasi semua halaman sidebar...\n\n";

foreach ($sidebarPages as $filename => $config) {
    echo "📄 Processing $filename...\n";
    updatePageWithModernTemplate($filename, $config);
}

echo "🎉 Semua halaman sidebar berhasil dimodernisasi!\n\n";
echo "📋 Summary Update:\n";
echo "   ✅ Header: Modern page header dengan breadcrumb navigation\n";
echo "   ✅ Icons: Icon yang sesuai dan konsisten untuk setiap halaman\n";
echo "   ✅ Layout: Responsive layout dengan modern container structure\n";
echo "   ✅ CSS: Modern CSS framework terintegrasi\n";
echo "   ✅ Footer: Consistent footer structure\n";
echo "   ✅ Actions: Modern action buttons dengan proper styling\n\n";
echo "📝 Catatan: Setiap halaman mungkin perlu penyesuaian konten spesifik.\n";
?>
