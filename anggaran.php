<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';

$currentUser = getCurrentUser();
if (!$currentUser) {
    setFlashMessage('danger', '<PERSON>si tidak valid. Silakan login kembali.');
    redirect('login.php');
}

$currentPage = 'anggaran';
$pageTitle = '<PERSON><PERSON><PERSON>';

// Create budgets table if not exists
try {
    $pdo->exec("CREATE TABLE IF NOT EXISTS budgets (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        name VARCHAR(255) NOT NULL,
        category_id INT,
        budget_amount DECIMAL(15,2) NOT NULL,
        spent_amount DECIMAL(15,2) DEFAULT 0,
        period_type ENUM('monthly', 'weekly', 'yearly') DEFAULT 'monthly',
        start_date DATE NOT NULL,
        end_date DATE NOT NULL,
        status ENUM('active', 'completed', 'exceeded', 'paused') DEFAULT 'active',
        alert_percentage INT DEFAULT 80,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        INDEX idx_user_period (user_id, start_date, end_date),
        INDEX idx_status (status)
    )");
} catch (PDOException $e) {
    error_log("Error creating budgets table: " . $e->getMessage());
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        switch ($_POST['action']) {
            case 'add_budget':
                $name = trim($_POST['name'] ?? '');
                $categoryId = $_POST['category_id'] ?? null;
                $budgetAmount = $_POST['budget_amount'] ?? 0;
                $periodType = $_POST['period_type'] ?? 'monthly';
                $startDate = $_POST['start_date'] ?? date('Y-m-01');
                $alertPercentage = $_POST['alert_percentage'] ?? 80;
                
                if (empty($name) || empty($budgetAmount)) {
                    throw new Exception('Nama anggaran dan jumlah anggaran harus diisi');
                }
                
                // Calculate end date based on period type
                $endDate = $startDate;
                switch ($periodType) {
                    case 'weekly':
                        $endDate = date('Y-m-d', strtotime($startDate . ' +6 days'));
                        break;
                    case 'monthly':
                        $endDate = date('Y-m-t', strtotime($startDate));
                        break;
                    case 'yearly':
                        $endDate = date('Y-12-31', strtotime($startDate));
                        break;
                }
                
                $stmt = $pdo->prepare("INSERT INTO budgets (user_id, name, category_id, budget_amount, period_type, start_date, end_date, alert_percentage) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
                $stmt->execute([$currentUser['id'], $name, $categoryId ?: null, $budgetAmount, $periodType, $startDate, $endDate, $alertPercentage]);
                
                setFlashMessage('success', 'Anggaran berhasil ditambahkan');
                break;
                
            case 'update_budget':
                $budgetId = $_POST['budget_id'] ?? '';
                $name = trim($_POST['name'] ?? '');
                $categoryId = $_POST['category_id'] ?? null;
                $budgetAmount = $_POST['budget_amount'] ?? 0;
                $alertPercentage = $_POST['alert_percentage'] ?? 80;
                $status = $_POST['status'] ?? 'active';
                
                if (empty($budgetId) || empty($name) || empty($budgetAmount)) {
                    throw new Exception('Data anggaran tidak lengkap');
                }
                
                $stmt = $pdo->prepare("UPDATE budgets SET name = ?, category_id = ?, budget_amount = ?, alert_percentage = ?, status = ? WHERE id = ? AND user_id = ?");
                $stmt->execute([$name, $categoryId ?: null, $budgetAmount, $alertPercentage, $status, $budgetId, $currentUser['id']]);
                
                setFlashMessage('success', 'Anggaran berhasil diperbarui');
                break;
                
            case 'delete_budget':
                $budgetId = $_POST['budget_id'] ?? '';
                if ($budgetId) {
                    $stmt = $pdo->prepare("DELETE FROM budgets WHERE id = ? AND user_id = ?");
                    $stmt->execute([$budgetId, $currentUser['id']]);
                    setFlashMessage('success', 'Anggaran berhasil dihapus');
                }
                break;
                
            case 'refresh_spending':
                // Update spent amounts based on actual transactions
                $stmt = $pdo->prepare("
                    UPDATE budgets b 
                    SET spent_amount = (
                        SELECT COALESCE(SUM(t.amount), 0) 
                        FROM transactions t 
                        WHERE t.user_id = b.user_id 
                        AND t.type = 'expense'
                        AND t.transaction_date BETWEEN b.start_date AND b.end_date
                        AND (b.category_id IS NULL OR t.category_id = b.category_id)
                    )
                    WHERE b.user_id = ?
                ");
                $stmt->execute([$currentUser['id']]);
                
                setFlashMessage('success', 'Data pengeluaran berhasil diperbarui');
                break;
        }
        redirect('anggaran.php');
    } catch (Exception $e) {
        setFlashMessage('danger', 'Error: ' . $e->getMessage());
    }
}

// Get user's budgets
$stmt = $pdo->prepare("
    SELECT b.*, c.name as category_name, c.color as category_color
    FROM budgets b
    LEFT JOIN categories c ON b.category_id = c.id
    WHERE b.user_id = ?
    ORDER BY b.start_date DESC, b.created_at DESC
");
$stmt->execute([$currentUser['id']]);
$budgets = $stmt->fetchAll();

// Get categories for dropdown
$stmt = $pdo->prepare("SELECT * FROM categories WHERE user_id = ? AND type = 'expense' ORDER BY name");
$stmt->execute([$currentUser['id']]);
$categories = $stmt->fetchAll();

// Calculate budget statistics
$totalBudget = 0;
$totalSpent = 0;
$activeBudgets = 0;
$exceededBudgets = 0;

foreach ($budgets as $budget) {
    if ($budget['status'] === 'active') {
        $totalBudget += $budget['budget_amount'];
        $totalSpent += $budget['spent_amount'];
        $activeBudgets++;
        
        if ($budget['spent_amount'] > $budget['budget_amount']) {
            $exceededBudgets++;
        }
    }
}

require_once 'includes/views/layouts/modern_page_template.php';
include 'includes/views/layouts/header.php';
?>

<div class="content-wrapper">
    <div class="container-fluid">
        <div class="row justify-content-center">
            <div class="col-12 col-xl-11 col-xxl-10">
                <div class="main-content-container">

                    <?php
                    // Page Header
                    renderModernPageHeader(
                        'Manajemen Anggaran',
                        'Rencanakan dan pantau pengeluaran Anda dengan sistem anggaran yang efektif',
                        [
                            ['label' => 'Home', 'url' => '/keuangan/dashboard.php', 'icon' => 'fas fa-home'],
                            ['label' => 'Anggaran', 'url' => '/keuangan/anggaran.php', 'icon' => 'fas fa-calculator']
                        ],
                        [
                            [
                                'type' => 'button',
                                'label' => 'Refresh Data',
                                'icon' => 'fas fa-sync',
                                'class' => 'btn-outline-info',
                                'onclick' => 'refreshSpending()'
                            ],
                            [
                                'type' => 'button',
                                'label' => 'Tambah Anggaran',
                                'icon' => 'fas fa-plus',
                                'class' => 'btn-primary',
                                'data-bs-toggle' => 'modal',
                                'data-bs-target' => '#addBudgetModal'
                            ]
                        ]
                    );
                    ?>
                    <!-- Statistics Cards -->
                    <?php
                    $remainingBudget = $totalBudget - $totalSpent;
                    $usagePercentage = $totalBudget > 0 ? ($totalSpent / $totalBudget) * 100 : 0;

                    $stats = [
                        [
                            'label' => 'Total Anggaran',
                            'value' => formatRupiah($totalBudget),
                            'icon' => 'fas fa-wallet',
                            'color' => 'primary',
                            'subtitle' => 'Anggaran keseluruhan'
                        ],
                        [
                            'label' => 'Total Terpakai',
                            'value' => formatRupiah($totalSpent),
                            'icon' => 'fas fa-credit-card',
                            'color' => 'warning',
                            'subtitle' => number_format($usagePercentage, 1) . '% dari anggaran'
                        ],
                        [
                            'label' => 'Anggaran Aktif',
                            'value' => number_format($activeBudgets),
                            'icon' => 'fas fa-chart-line',
                            'color' => 'success',
                            'subtitle' => 'Sedang berjalan'
                        ],
                        [
                            'label' => 'Sisa Anggaran',
                            'value' => formatRupiah($remainingBudget),
                            'icon' => 'fas fa-piggy-bank',
                            'color' => $remainingBudget >= 0 ? 'info' : 'danger',
                            'subtitle' => $exceededBudgets > 0 ? $exceededBudgets . ' terlampaui' : 'Dalam batas'
                        ]
                    ];

                    renderModernStatsCards($stats);
                    ?>
        
        <!-- Budgets List -->
        <div class="col-12">
            <?php if (empty($budgets)): ?>
                <div class="card border-0 shadow-sm">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-chart-pie fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Belum ada anggaran</h5>
                        <p class="text-muted">Mulai buat anggaran untuk mengontrol pengeluaran Anda</p>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addBudgetModal">
                            <i class="fas fa-plus me-2"></i>Buat Anggaran Pertama
                        </button>
                    </div>
                </div>
            <?php else: ?>
                <div class="row">
                    <?php foreach ($budgets as $budget): ?>
                        <?php 
                        $percentage = $budget['budget_amount'] > 0 ? ($budget['spent_amount'] / $budget['budget_amount']) * 100 : 0;
                        $progressClass = $percentage >= 100 ? 'danger' : ($percentage >= $budget['alert_percentage'] ? 'warning' : 'success');
                        $statusClass = $budget['status'] === 'active' ? 'primary' : ($budget['status'] === 'exceeded' ? 'danger' : 'secondary');
                        $remaining = $budget['budget_amount'] - $budget['spent_amount'];
                        ?>
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card border-0 shadow-sm h-100">
                                <div class="card-header bg-<?= $statusClass ?> text-white">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <h6 class="mb-0"><?= htmlspecialchars($budget['name']) ?></h6>
                                        <span class="badge bg-light text-dark"><?= ucfirst($budget['status']) ?></span>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <div class="d-flex justify-content-between align-items-center mb-1">
                                            <small class="text-muted">Periode</small>
                                            <small><?= ucfirst($budget['period_type']) ?></small>
                                        </div>
                                        <small class="text-muted">
                                            <?= date('d/m/Y', strtotime($budget['start_date'])) ?> - 
                                            <?= date('d/m/Y', strtotime($budget['end_date'])) ?>
                                        </small>
                                    </div>
                                    
                                    <?php if ($budget['category_name']): ?>
                                        <div class="mb-3">
                                            <span class="badge" style="background-color: <?= $budget['category_color'] ?>">
                                                <?= htmlspecialchars($budget['category_name']) ?>
                                            </span>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <div class="mb-3">
                                        <div class="d-flex justify-content-between mb-1">
                                            <small>Penggunaan</small>
                                            <small><?= number_format($percentage, 1) ?>%</small>
                                        </div>
                                        <div class="progress">
                                            <div class="progress-bar bg-<?= $progressClass ?>" style="width: <?= min($percentage, 100) ?>%"></div>
                                        </div>
                                    </div>
                                    
                                    <div class="row text-center mb-3">
                                        <div class="col-6">
                                            <small class="text-muted">Terpakai</small>
                                            <div class="fw-bold text-<?= $progressClass ?>">
                                                Rp <?= number_format($budget['spent_amount'], 0, ',', '.') ?>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <small class="text-muted">Anggaran</small>
                                            <div class="fw-bold">
                                                Rp <?= number_format($budget['budget_amount'], 0, ',', '.') ?>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="text-center mb-3">
                                        <small class="text-muted">Sisa Anggaran</small>
                                        <div class="fw-bold text-<?= $remaining >= 0 ? 'success' : 'danger' ?>">
                                            Rp <?= number_format($remaining, 0, ',', '.') ?>
                                        </div>
                                    </div>
                                    
                                    <?php if ($percentage >= $budget['alert_percentage'] && $budget['status'] === 'active'): ?>
                                        <div class="alert alert-warning alert-sm mb-3">
                                            <i class="fas fa-exclamation-triangle me-1"></i>
                                            <small>Mendekati batas anggaran!</small>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <div class="btn-group btn-group-sm w-100">
                                        <button type="button" class="btn btn-outline-primary" onclick="editBudget(<?= htmlspecialchars(json_encode($budget)) ?>)">
                                            <i class="fas fa-edit"></i> Edit
                                        </button>
                                        <form method="POST" class="d-inline" onsubmit="return confirm('Yakin ingin menghapus anggaran ini?')">
                                            <input type="hidden" name="action" value="delete_budget">
                                            <input type="hidden" name="budget_id" value="<?= $budget['id'] ?>">
                                            <button type="submit" class="btn btn-outline-danger">
                                                <i class="fas fa-trash"></i> Hapus
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Add Budget Modal -->
<div class="modal fade" id="addBudgetModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Tambah Anggaran</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="add_budget">
                    
                    <div class="mb-3">
                        <label class="form-label">Nama Anggaran</label>
                        <input type="text" class="form-control" name="name" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Kategori (Opsional)</label>
                        <select class="form-select" name="category_id">
                            <option value="">Semua Kategori</option>
                            <?php foreach ($categories as $category): ?>
                                <option value="<?= $category['id'] ?>"><?= htmlspecialchars($category['name']) ?></option>
                            <?php endforeach; ?>
                        </select>
                        <small class="text-muted">Kosongkan untuk anggaran umum</small>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Jumlah Anggaran</label>
                        <div class="input-group">
                            <span class="input-group-text">Rp</span>
                            <input type="number" class="form-control" name="budget_amount" step="0.01" min="0" required>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Periode</label>
                                <select class="form-select" name="period_type" required>
                                    <option value="weekly">Mingguan</option>
                                    <option value="monthly" selected>Bulanan</option>
                                    <option value="yearly">Tahunan</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Tanggal Mulai</label>
                                <input type="date" class="form-control" name="start_date" value="<?= date('Y-m-01') ?>" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Peringatan pada (%)</label>
                        <input type="number" class="form-control" name="alert_percentage" value="80" min="1" max="100" required>
                        <small class="text-muted">Peringatan akan muncul saat mencapai persentase ini</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Simpan Anggaran</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Budget Modal -->
<div class="modal fade" id="editBudgetModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Anggaran</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="update_budget">
                    <input type="hidden" name="budget_id" id="edit_budget_id">
                    
                    <div class="mb-3">
                        <label class="form-label">Nama Anggaran</label>
                        <input type="text" class="form-control" name="name" id="edit_name" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Kategori (Opsional)</label>
                        <select class="form-select" name="category_id" id="edit_category_id">
                            <option value="">Semua Kategori</option>
                            <?php foreach ($categories as $category): ?>
                                <option value="<?= $category['id'] ?>"><?= htmlspecialchars($category['name']) ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Jumlah Anggaran</label>
                        <div class="input-group">
                            <span class="input-group-text">Rp</span>
                            <input type="number" class="form-control" name="budget_amount" id="edit_budget_amount" step="0.01" min="0" required>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Status</label>
                        <select class="form-select" name="status" id="edit_status" required>
                            <option value="active">Aktif</option>
                            <option value="completed">Selesai</option>
                            <option value="exceeded">Terlampaui</option>
                            <option value="paused">Ditunda</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Peringatan pada (%)</label>
                        <input type="number" class="form-control" name="alert_percentage" id="edit_alert_percentage" min="1" max="100" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Update Anggaran</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function editBudget(budget) {
    document.getElementById('edit_budget_id').value = budget.id;
    document.getElementById('edit_name').value = budget.name;
    document.getElementById('edit_category_id').value = budget.category_id || '';
    document.getElementById('edit_budget_amount').value = budget.budget_amount;
    document.getElementById('edit_status').value = budget.status;
    document.getElementById('edit_alert_percentage').value = budget.alert_percentage;
    
    const modal = new bootstrap.Modal(document.getElementById('editBudgetModal'));
    modal.show();
}
</script>

<?php include 'includes/views/layouts/footer.php'; ?>
