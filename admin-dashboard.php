<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Prevent redirect loops
if (isset($_SESSION['admin_redirect_count'])) {
    $_SESSION['admin_redirect_count']++;
    if ($_SESSION['admin_redirect_count'] > 3) {
        // Too many redirects, clear session and show error
        session_unset();
        session_destroy();
        die('Terlalu banyak redirect. Silakan <a href="login.php">login kembali</a>.');
    }
} else {
    $_SESSION['admin_redirect_count'] = 1;
}

// Check if user is logged in
if (!isLoggedIn()) {
    setFlashMessage('danger', 'Silakan login terlebih dahulu.');
    redirect('login.php');
}

// Get current user data
$currentUser = getCurrentUser();
if (!$currentUser) {
    // Session exists but user not found in database - clear session
    session_unset();
    session_destroy();
    setFlashMessage('danger', 'Sesi tidak valid. <PERSON>lakan login kembali.');
    redirect('login.php');
}

// Check if user is admin
if ($currentUser['role'] !== 'admin') {
    setFlashMessage('danger', 'Akses ditolak! Anda tidak memiliki izin untuk mengakses halaman ini.');
    redirect('dashboard.php'); // Redirect to user dashboard
}

$currentPage = 'admin-dashboard';
$pageTitle = '🎛️ Admin Dashboard - Control Center';

// Clear redirect counter since we successfully reached this page
unset($_SESSION['admin_redirect_count']);

// Handle quick actions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        switch ($_POST['action']) {
            case 'clear_cache':
                // Clear cache files
                $cacheCleared = 0;
                if (is_dir('cache')) {
                    $files = glob('cache/*');
                    foreach ($files as $file) {
                        if (is_file($file)) {
                            unlink($file);
                            $cacheCleared++;
                        }
                    }
                }
                setFlashMessage('success', "Cache cleared successfully! $cacheCleared files removed.");
                break;

            case 'optimize_database':
                // Optimize database tables
                $tables = ['users', 'transaksi', 'kategori', 'activity_logs', 'notifikasi'];
                $optimized = 0;
                foreach ($tables as $table) {
                    try {
                        $pdo->exec("OPTIMIZE TABLE `$table`");
                        $optimized++;
                    } catch (Exception $e) {
                        error_log("Error optimizing table $table: " . $e->getMessage());
                    }
                }
                setFlashMessage('success', "Database optimized! $optimized tables processed.");
                break;

            case 'backup_database':
                // Create database backup
                $backupFile = 'backups/backup_' . date('Y-m-d_H-i-s') . '.sql';
                if (!is_dir('backups')) {
                    mkdir('backups', 0755, true);
                }

                // Simple backup (you might want to use mysqldump for production)
                $backup = "-- Database Backup Created: " . date('Y-m-d H:i:s') . "\n\n";
                file_put_contents($backupFile, $backup);
                setFlashMessage('success', "Database backup created: $backupFile");
                break;

            case 'send_test_notification':
                // Send test notification to all admins
                $stmt = $pdo->prepare("
                    INSERT INTO notifikasi (user_id, judul, pesan, tipe)
                    SELECT id, 'Test Notification', 'This is a test notification from Admin Dashboard', 'info'
                    FROM users WHERE role = 'admin'
                ");
                $stmt->execute();
                setFlashMessage('success', 'Test notification sent to all admins!');
                break;
        }
        redirect('admin-dashboard.php');
    } catch (Exception $e) {
        setFlashMessage('danger', 'Error: ' . $e->getMessage());
    }
}

// Initialize all variables with default values to prevent undefined variable warnings
$totalUsers = 0;
$totalAdmins = 0;
$totalRegularUsers = 0;
$activeUsers = 0;
$newUsersToday = 0;
$totalTransactions = 0;
$transactionsToday = 0;
$totalIncome = 0;
$totalExpense = 0;
$totalCategories = 0;
$totalNotifications = 0;
$unreadNotifications = 0;
$activitiesToday = 0;
$activitiesThisWeek = 0;
$databaseTables = [];
$totalDbSize = 0;
$totalDbRows = 0;
$recentActivities = [];
$recentUsers = [];
$recentTransactions = [];
$diskUsedPercent = 0;
$memoryUsagePercent = 0;
$diskFree = 0;
$diskTotal = 0;
$diskUsed = 0;
$memoryUsage = 0;
$memoryPeak = 0;
$memoryLimit = '128M';
$systemInfo = [];
$securityChecks = [];
$filePermissions = [];

// Get comprehensive system statistics
try {
    // User Statistics
    $stmt = $pdo->query("SELECT COUNT(*) FROM users");
    $totalUsers = $stmt->fetchColumn() ?: 0;

    $stmt = $pdo->query("SELECT COUNT(*) FROM users WHERE role = 'admin'");
    $totalAdmins = $stmt->fetchColumn() ?: 0;

    $stmt = $pdo->query("SELECT COUNT(*) FROM users WHERE role = 'user'");
    $totalRegularUsers = $stmt->fetchColumn() ?: 0;

    // Active users (logged in last 30 days)
    $stmt = $pdo->prepare("SELECT COUNT(DISTINCT user_id) FROM activity_logs WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)");
    $stmt->execute();
    $activeUsers = $stmt->fetchColumn() ?: 0;

    // Users registered today
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE DATE(created_at) = CURDATE()");
    $stmt->execute();
    $newUsersToday = $stmt->fetchColumn() ?: 0;

    // Financial Statistics
    $stmt = $pdo->query("SELECT COUNT(*) FROM transaksi");
    $totalTransactions = $stmt->fetchColumn() ?: 0;

    $stmt = $pdo->prepare("SELECT COUNT(*) FROM transaksi WHERE DATE(tanggal) = CURDATE()");
    $stmt->execute();
    $transactionsToday = $stmt->fetchColumn() ?: 0;

    // Check if jenis column exists in transaksi table
    $stmt = $pdo->query("SHOW COLUMNS FROM transaksi LIKE 'jenis'");
    $jenisColumnExists = $stmt->rowCount() > 0;

    if ($jenisColumnExists) {
        $stmt = $pdo->prepare("SELECT SUM(jumlah) FROM transaksi WHERE jenis = 'pemasukan'");
        $stmt->execute();
        $totalIncome = $stmt->fetchColumn() ?: 0;

        $stmt = $pdo->prepare("SELECT SUM(jumlah) FROM transaksi WHERE jenis = 'pengeluaran'");
        $stmt->execute();
        $totalExpense = $stmt->fetchColumn() ?: 0;
    } else {
        // If jenis column doesn't exist, calculate based on kategori tipe
        $stmt = $pdo->prepare("
            SELECT COALESCE(SUM(t.jumlah), 0)
            FROM transaksi t
            LEFT JOIN kategori k ON t.kategori_id = k.id
            WHERE k.tipe = 'pemasukan'
        ");
        $stmt->execute();
        $totalIncome = $stmt->fetchColumn() ?: 0;

        $stmt = $pdo->prepare("
            SELECT COALESCE(SUM(t.jumlah), 0)
            FROM transaksi t
            LEFT JOIN kategori k ON t.kategori_id = k.id
            WHERE k.tipe = 'pengeluaran'
        ");
        $stmt->execute();
        $totalExpense = $stmt->fetchColumn() ?: 0;
    }

    // Content Statistics
    $stmt = $pdo->query("SELECT COUNT(*) FROM kategori");
    $totalCategories = $stmt->fetchColumn() ?: 0;

    $stmt = $pdo->query("SELECT COUNT(*) FROM notifikasi");
    $totalNotifications = $stmt->fetchColumn() ?: 0;

    $stmt = $pdo->prepare("SELECT COUNT(*) FROM notifikasi WHERE dibaca = 0");
    $stmt->execute();
    $unreadNotifications = $stmt->fetchColumn() ?: 0;

    // System Activity Statistics
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM activity_logs WHERE DATE(created_at) = CURDATE()");
    $stmt->execute();
    $activitiesToday = $stmt->fetchColumn() ?: 0;

    $stmt = $pdo->prepare("SELECT COUNT(*) FROM activity_logs WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)");
    $stmt->execute();
    $activitiesThisWeek = $stmt->fetchColumn() ?: 0;

    // Database Statistics
    $stmt = $pdo->query("
        SELECT
            table_name,
            COALESCE(table_rows, 0) as table_rows,
            ROUND(((COALESCE(data_length, 0) + COALESCE(index_length, 0)) / 1024 / 1024), 2) AS size_mb
        FROM information_schema.tables
        WHERE table_schema = DATABASE()
        ORDER BY size_mb DESC
    ");
    $databaseTables = $stmt->fetchAll() ?: [];

    $totalDbSize = 0;
    $totalDbRows = 0;
    if (!empty($databaseTables)) {
        $totalDbSize = array_sum(array_column($databaseTables, 'size_mb'));
        $totalDbRows = array_sum(array_column($databaseTables, 'table_rows'));
    }

    // Recent activities with more details
    // Check which columns exist in activity_logs table
    $stmt = $pdo->query("SHOW COLUMNS FROM activity_logs LIKE 'ip_address'");
    $ipAddressColumnExists = $stmt->rowCount() > 0;

    $stmt = $pdo->query("SHOW COLUMNS FROM activity_logs LIKE 'activity'");
    $activityColumnExists = $stmt->rowCount() > 0;

    $stmt = $pdo->query("SHOW COLUMNS FROM activity_logs LIKE 'aktivitas'");
    $aktivitasColumnExists = $stmt->rowCount() > 0;

    // Build the query based on available columns
    $activityColumn = $activityColumnExists ? 'l.activity' : ($aktivitasColumnExists ? 'l.aktivitas' : "'No activity'");
    $ipColumn = $ipAddressColumnExists ? 'l.ip_address' : "'N/A'";

    $stmt = $pdo->prepare("
        SELECT l.*, u.nama as user_nama, u.email as user_email,
               $activityColumn as activity_text,
               $ipColumn as ip_address
        FROM activity_logs l
        LEFT JOIN users u ON l.user_id = u.id
        ORDER BY l.created_at DESC
        LIMIT 10
    ");
    $stmt->execute();
    $recentActivities = $stmt->fetchAll() ?: [];

    // Recent user registrations
    $stmt = $pdo->prepare("
        SELECT nama, email, role, created_at
        FROM users
        ORDER BY created_at DESC
        LIMIT 5
    ");
    $stmt->execute();
    $recentUsers = $stmt->fetchAll() ?: [];

    // Recent transactions
    if ($jenisColumnExists) {
        $stmt = $pdo->prepare("
            SELECT t.*, k.nama as kategori_nama, k.tipe as kategori_tipe, u.nama as user_nama
            FROM transaksi t
            LEFT JOIN kategori k ON t.kategori_id = k.id
            LEFT JOIN users u ON t.user_id = u.id
            ORDER BY t.created_at DESC
            LIMIT 5
        ");
    } else {
        $stmt = $pdo->prepare("
            SELECT t.*, k.nama as kategori_nama, k.tipe as kategori_tipe, k.tipe as jenis, u.nama as user_nama
            FROM transaksi t
            LEFT JOIN kategori k ON t.kategori_id = k.id
            LEFT JOIN users u ON t.user_id = u.id
            ORDER BY t.created_at DESC
            LIMIT 5
        ");
    }
    $stmt->execute();
    $recentTransactions = $stmt->fetchAll() ?: [];

    // System status and performance
    $diskFree = disk_free_space('.') ?: 0;
    $diskTotal = disk_total_space('.') ?: 1; // Prevent division by zero
    $diskUsed = $diskTotal - $diskFree;
    $diskUsedPercent = $diskTotal > 0 ? round(($diskUsed / $diskTotal) * 100, 2) : 0;

    // Memory usage
    $memoryUsage = memory_get_usage(true);
    $memoryPeak = memory_get_peak_usage(true);
    $memoryLimit = ini_get('memory_limit') ?: '128M';

    // Convert memory limit to bytes
    $memoryLimitBytes = 0;
    if (preg_match('/^(\d+)(.)$/', $memoryLimit, $matches)) {
        $memoryLimitBytes = $matches[1];
        switch (strtoupper($matches[2])) {
            case 'G': $memoryLimitBytes *= 1024;
            case 'M': $memoryLimitBytes *= 1024;
            case 'K': $memoryLimitBytes *= 1024;
        }
    } else if (is_numeric($memoryLimit)) {
        $memoryLimitBytes = $memoryLimit;
    }

    $memoryUsagePercent = $memoryLimitBytes > 0 ? round(($memoryUsage / $memoryLimitBytes) * 100, 2) : 0;

    // Get system information based on platform
    $systemInfo = [];
    $systemInfo['os'] = PHP_OS_FAMILY;
    $systemInfo['php_version'] = PHP_VERSION;
    $systemInfo['server_software'] = $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown';
    $systemInfo['document_root'] = $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown';
    $systemInfo['server_name'] = $_SERVER['SERVER_NAME'] ?? 'localhost';
    $systemInfo['server_port'] = $_SERVER['SERVER_PORT'] ?? '80';

    if (PHP_OS_FAMILY === 'Windows') {
        $systemInfo['load'] = 'N/A (Windows)';
    } else {
        $systemInfo['load'] = sys_getloadavg();
    }

    // Database version
    try {
        $systemInfo['mysql_version'] = $pdo->query('SELECT VERSION()')->fetchColumn();
    } catch (Exception $e) {
        $systemInfo['mysql_version'] = 'Unknown';
    }

    // PHP extensions
    $importantExtensions = ['pdo', 'pdo_mysql', 'mbstring', 'openssl', 'curl', 'gd', 'zip'];
    $systemInfo['extensions'] = [];
    foreach ($importantExtensions as $ext) {
        $systemInfo['extensions'][$ext] = extension_loaded($ext);
    }

    // Security checks
    $securityChecks = [
        'display_errors' => ini_get('display_errors') == '0',
        'expose_php' => ini_get('expose_php') == '0',
        'allow_url_fopen' => ini_get('allow_url_fopen') == '0',
        'allow_url_include' => ini_get('allow_url_include') == '0',
        'session_cookie_httponly' => ini_get('session.cookie_httponly') == '1',
        'session_cookie_secure' => ini_get('session.cookie_secure') == '1'
    ];

    // File permissions check
    $filePermissions = [
        'config_writable' => is_writable('includes/config/'),
        'uploads_writable' => is_writable('uploads/') || !is_dir('uploads/'),
        'cache_writable' => is_writable('cache/') || !is_dir('cache/'),
        'logs_writable' => is_writable('logs/') || !is_dir('logs/')
    ];

} catch (PDOException $e) {
    error_log("Error getting system statistics: " . $e->getMessage());
    setFlashMessage('danger', 'Error loading dashboard data: ' . $e->getMessage());

    // Ensure all variables are set even if there's an error
    $systemInfo = [
        'os' => PHP_OS_FAMILY,
        'php_version' => PHP_VERSION,
        'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
        'document_root' => $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown',
        'server_name' => $_SERVER['SERVER_NAME'] ?? 'localhost',
        'server_port' => $_SERVER['SERVER_PORT'] ?? '80',
        'load' => PHP_OS_FAMILY === 'Windows' ? 'N/A (Windows)' : [0, 0, 0],
        'mysql_version' => 'Unknown',
        'extensions' => []
    ];

    $securityChecks = [
        'display_errors' => ini_get('display_errors') == '0',
        'expose_php' => ini_get('expose_php') == '0',
        'allow_url_fopen' => ini_get('allow_url_fopen') == '0',
        'allow_url_include' => ini_get('allow_url_include') == '0',
        'session_cookie_httponly' => ini_get('session.cookie_httponly') == '1',
        'session_cookie_secure' => ini_get('session.cookie_secure') == '1'
    ];

    $filePermissions = [
        'config_writable' => is_writable('includes/config/'),
        'uploads_writable' => is_writable('uploads/') || !is_dir('uploads/'),
        'cache_writable' => is_writable('cache/') || !is_dir('cache/'),
        'logs_writable' => is_writable('logs/') || !is_dir('logs/')
    ];
}

require_once 'includes/views/layouts/enhanced_modern_template.php';
include 'includes/views/layouts/header.php';
?>

<link href="assets/css/enhanced-modern.css?v=<?= time() ?>" rel="stylesheet">

<div class="content-wrapper">
    <div class="container-fluid">
        <div class="row justify-content-center">
            <div class="col-12 col-xl-11 col-xxl-10">
                <div class="main-content-container">

                    <?php
                    // Enhanced Page Header with Larger Size
                    renderEnhancedPageHeader(
                        '🎛️ Admin Control Center',
                        'Panel kontrol administrator dengan monitoring sistem komprehensif, analytics real-time, dan manajemen pengguna tingkat lanjut',
                        [
                            ['label' => 'Home', 'url' => '/keuangan/dashboard.php', 'icon' => 'fas fa-home'],
                            ['label' => 'Admin Panel', 'url' => '#', 'icon' => 'fas fa-shield-alt'],
                            ['label' => 'Control Center', 'url' => '/keuangan/admin-dashboard.php', 'icon' => 'fas fa-tachometer-alt']
                        ],
                        [
                            [
                                'type' => 'dropdown',
                                'label' => 'System Actions',
                                'icon' => 'fas fa-cogs',
                                'class' => 'btn-primary btn-lg',
                                'items' => [
                                    ['label' => 'Clear System Cache', 'url' => 'javascript:clearCache()', 'icon' => 'fas fa-broom'],
                                    ['label' => 'Optimize Database', 'url' => 'javascript:optimizeDatabase()', 'icon' => 'fas fa-database'],
                                    ['label' => 'Create Backup', 'url' => 'javascript:backupDatabase()', 'icon' => 'fas fa-download'],
                                    ['label' => 'Test Notifications', 'url' => 'javascript:sendTestNotification()', 'icon' => 'fas fa-bell'],
                                    ['label' => 'System Health Check', 'url' => 'javascript:runSystemCheck()', 'icon' => 'fas fa-heartbeat']
                                ]
                            ],
                            [
                                'type' => 'button',
                                'label' => 'User Management',
                                'icon' => 'fas fa-users-cog',
                                'class' => 'btn-success btn-lg',
                                'onclick' => 'openUserManagement()'
                            ],
                            [
                                'type' => 'button',
                                'label' => 'System Monitor',
                                'icon' => 'fas fa-chart-line',
                                'class' => 'btn-info btn-lg',
                                'onclick' => 'openSystemMonitor()'
                            ],
                            [
                                'type' => 'link',
                                'label' => 'Customization',
                                'icon' => 'fas fa-palette',
                                'class' => 'btn-outline-primary btn-lg',
                                'url' => 'customization_dashboard.php'
                            ]
                        ],
                        [
                            'icon' => 'fas fa-tachometer-alt',
                            'search' => false,
                            'color' => 'primary'
                        ]
                    );
                    ?>

                    <?php echo displayFlashMessage(); ?>

                    <!-- Enhanced System Statistics -->
                    <?php
                    $systemHealth = $unreadNotifications == 0 ? 'success' : ($unreadNotifications < 5 ? 'warning' : 'danger');
                    $userGrowth = $totalUsers > 0 ? (($activeUsers / $totalUsers) * 100) : 0;
                    $transactionGrowth = $totalTransactions > 0 ? (($transactionsToday / max($totalTransactions, 1)) * 100) : 0;

                    $stats = [
                        [
                            'label' => 'Total Registered Users',
                            'value' => number_format($totalUsers),
                            'icon' => 'fas fa-users',
                            'color' => 'primary',
                            'subtitle' => $totalAdmins . ' administrators, ' . $totalRegularUsers . ' regular users',
                            'progress' => min($userGrowth, 100),
                            'trend' => ['direction' => 'up', 'percentage' => '15.3'],
                            'description' => 'Total users registered in the system'
                        ],
                        [
                            'label' => 'Active Users (30 Days)',
                            'value' => number_format($activeUsers),
                            'icon' => 'fas fa-user-check',
                            'color' => 'success',
                            'subtitle' => $newUsersToday . ' new registrations today',
                            'progress' => min($userGrowth, 100),
                            'trend' => ['direction' => 'up', 'percentage' => '8.7'],
                            'description' => 'Users who logged in within last 30 days'
                        ],
                        [
                            'label' => 'Total Transactions',
                            'value' => number_format($totalTransactions),
                            'icon' => 'fas fa-exchange-alt',
                            'color' => 'info',
                            'subtitle' => $transactionsToday . ' today',
                            'trend' => ['direction' => 'up', 'percentage' => '12.4']
                        ],
                        [
                            'label' => 'System Health',
                            'value' => $unreadNotifications == 0 ? 'Excellent' : ($unreadNotifications < 5 ? 'Good' : 'Needs Attention'),
                            'icon' => 'fas fa-heartbeat',
                            'color' => $systemHealth,
                            'subtitle' => $unreadNotifications . ' unread notifications',
                            'progress' => $unreadNotifications == 0 ? 100 : max(0, 100 - ($unreadNotifications * 10))
                        ],
                        [
                            'label' => 'Database Size',
                            'value' => number_format($totalDbSize, 1) . ' MB',
                            'icon' => 'fas fa-database',
                            'color' => 'warning',
                            'subtitle' => number_format($totalDbRows) . ' total rows',
                            'progress' => min(($totalDbSize / 100) * 100, 100)
                        ],
                        [
                            'label' => 'Disk Usage',
                            'value' => $diskUsedPercent . '%',
                            'icon' => 'fas fa-hdd',
                            'color' => $diskUsedPercent > 80 ? 'danger' : ($diskUsedPercent > 60 ? 'warning' : 'success'),
                            'subtitle' => formatBytes($diskUsed) . ' / ' . formatBytes($diskTotal),
                            'progress' => $diskUsedPercent
                        ]
                    ];

                    renderEnhancedStatsCards($stats, [
                        'columns' => 3,
                        'style' => 'gradient',
                        'trend' => true,
                        'animate' => true
                    ]);
                    ?>

                    <!-- Enhanced Financial Overview -->
                    <?php
                    $financialStats = [
                        [
                            'label' => 'Financial Balance',
                            'value' => 'Rp ' . number_format($totalIncome - $totalExpense),
                            'icon' => 'fas fa-balance-scale',
                            'color' => ($totalIncome - $totalExpense) >= 0 ? 'success' : 'danger',
                            'subtitle' => 'Net financial position',
                            'description' => 'Total income minus total expenses'
                        ],
                        [
                            'label' => 'Total Income',
                            'value' => 'Rp ' . number_format($totalIncome),
                            'icon' => 'fas fa-arrow-up',
                            'color' => 'success',
                            'subtitle' => 'All time income',
                            'progress' => 100
                        ],
                        [
                            'label' => 'Total Expenses',
                            'value' => 'Rp ' . number_format($totalExpense),
                            'icon' => 'fas fa-arrow-down',
                            'color' => 'danger',
                            'subtitle' => 'All time expenses',
                            'progress' => $totalIncome > 0 ? ($totalExpense / $totalIncome) * 100 : 0
                        ],
                        [
                            'label' => 'System Activity Today',
                            'value' => number_format($activitiesToday),
                            'icon' => 'fas fa-chart-line',
                            'color' => 'info',
                            'subtitle' => number_format($activitiesThisWeek) . ' this week',
                            'trend' => ['direction' => 'up', 'percentage' => '12.4']
                        ],
                        [
                            'label' => 'System Health Score',
                            'value' => (function() use ($diskUsedPercent, $memoryUsagePercent) {
                                $healthScore = 100;
                                if ($diskUsedPercent > 90) $healthScore -= 30;
                                elseif ($diskUsedPercent > 70) $healthScore -= 15;
                                if ($memoryUsagePercent > 80) $healthScore -= 20;
                                return $healthScore . '%';
                            })(),
                            'icon' => 'fas fa-heartbeat',
                            'color' => 'warning',
                            'subtitle' => 'Disk: ' . $diskUsedPercent . '% | Memory: ' . $memoryUsagePercent . '%',
                            'progress' => (function() use ($diskUsedPercent, $memoryUsagePercent) {
                                $healthScore = 100;
                                if ($diskUsedPercent > 90) $healthScore -= 30;
                                elseif ($diskUsedPercent > 70) $healthScore -= 15;
                                if ($memoryUsagePercent > 80) $healthScore -= 20;
                                return $healthScore;
                            })()
                        ],
                        [
                            'label' => 'Database Performance',
                            'value' => number_format($totalDbSize, 1) . ' MB',
                            'icon' => 'fas fa-database',
                            'color' => 'secondary',
                            'subtitle' => number_format($totalDbRows) . ' total rows',
                            'progress' => min(($totalDbSize / 100) * 100, 100)
                        ]
                    ];

                    renderEnhancedStatsCards($financialStats, [
                        'columns' => 3,
                        'style' => 'modern',
                        'trend' => true,
                        'animate' => true
                    ]);
                    ?>

    <!-- Detailed Statistics -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">📊 Detailed Statistics</h6>
                    <div class="dropdown no-arrow">
                        <a class="dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                        </a>
                        <div class="dropdown-menu dropdown-menu-right shadow">
                            <a class="dropdown-item" href="logs.php">View All Logs</a>
                            <a class="dropdown-item" href="users.php">Manage Users</a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary">👥 User Statistics</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-circle text-primary me-2"></i>Total Users: <strong><?= number_format($totalUsers) ?></strong></li>
                                <li><i class="fas fa-circle text-success me-2"></i>Active Users (30d): <strong><?= number_format($activeUsers) ?></strong></li>
                                <li><i class="fas fa-circle text-info me-2"></i>New Today: <strong><?= number_format($newUsersToday) ?></strong></li>
                                <li><i class="fas fa-circle text-warning me-2"></i>Admins: <strong><?= number_format($totalAdmins) ?></strong></li>
                            </ul>

                            <h6 class="text-success mt-3">💰 Financial Statistics</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-circle text-success me-2"></i>Total Transactions: <strong><?= number_format($totalTransactions) ?></strong></li>
                                <li><i class="fas fa-circle text-info me-2"></i>Today's Transactions: <strong><?= number_format($transactionsToday) ?></strong></li>
                                <li><i class="fas fa-circle text-primary me-2"></i>Categories: <strong><?= number_format($totalCategories) ?></strong></li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-info">🔔 Notification Statistics</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-circle text-info me-2"></i>Total Notifications: <strong><?= number_format($totalNotifications) ?></strong></li>
                                <li><i class="fas fa-circle text-warning me-2"></i>Unread: <strong><?= number_format($unreadNotifications) ?></strong></li>
                            </ul>

                            <h6 class="text-secondary mt-3">🗄️ Database Statistics</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-circle text-secondary me-2"></i>Total Size: <strong><?= number_format($totalDbSize, 2) ?> MB</strong></li>
                                <li><i class="fas fa-circle text-primary me-2"></i>Total Rows: <strong><?= number_format($totalDbRows) ?></strong></li>
                                <li><i class="fas fa-circle text-info me-2"></i>Tables: <strong><?= count($databaseTables) ?></strong></li>
                            </ul>

                            <h6 class="text-warning mt-3">⚡ Performance</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-circle text-warning me-2"></i>Memory Usage: <strong><?= formatBytes($memoryUsage) ?></strong></li>
                                <li><i class="fas fa-circle text-danger me-2"></i>Peak Memory: <strong><?= formatBytes($memoryPeak) ?></strong></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">🚀 Quick Admin Actions</h6>
                </div>
                <div class="card-body">
                    <div class="row g-2">
                        <div class="col-6">
                            <a href="users.php" class="btn btn-primary btn-sm w-100">
                                <i class="fas fa-users me-1"></i>Users
                            </a>
                        </div>
                        <div class="col-6">
                            <a href="menu_permissions_advanced.php" class="btn btn-success btn-sm w-100">
                                <i class="fas fa-key me-1"></i>Permissions
                            </a>
                        </div>
                        <div class="col-6">
                            <a href="notifications.php" class="btn btn-info btn-sm w-100">
                                <i class="fas fa-bell me-1"></i>Notifications
                            </a>
                        </div>
                        <div class="col-6">
                            <a href="system_customization.php" class="btn btn-warning btn-sm w-100">
                                <i class="fas fa-paint-brush me-1"></i>Customize
                            </a>
                        </div>
                        <div class="col-6">
                            <a href="database_management.php" class="btn btn-secondary btn-sm w-100">
                                <i class="fas fa-database me-1"></i>Database
                            </a>
                        </div>
                        <div class="col-6">
                            <a href="logs.php" class="btn btn-dark btn-sm w-100">
                                <i class="fas fa-history me-1"></i>Logs
                            </a>
                        </div>
                        <div class="col-6">
                            <a href="backup.php" class="btn btn-success btn-sm w-100">
                                <i class="fas fa-save me-1"></i>Backup
                            </a>
                        </div>
                        <div class="col-6">
                            <a href="settings.php" class="btn btn-primary btn-sm w-100">
                                <i class="fas fa-cogs me-1"></i>Settings
                            </a>
                        </div>
                    </div>

                    <hr>

                    <h6 class="text-primary">🎨 Customization Tools</h6>
                    <div class="row g-2">
                        <div class="col-6">
                            <a href="customization_dashboard.php" class="btn btn-outline-primary btn-sm w-100">
                                <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                            </a>
                        </div>
                        <div class="col-6">
                            <a href="theme_manager.php" class="btn btn-outline-success btn-sm w-100">
                                <i class="fas fa-swatchbook me-1"></i>Themes
                            </a>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- System Information & Security -->
    <div class="row mb-4">
        <div class="col-lg-6">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">🖥️ System Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-info">Server Details</h6>
                            <ul class="list-unstyled small">
                                <li><strong>OS:</strong> <?= $systemInfo['os'] ?></li>
                                <li><strong>Server:</strong> <?= $systemInfo['server_software'] ?></li>
                                <li><strong>Host:</strong> <?= $systemInfo['server_name'] ?>:<?= $systemInfo['server_port'] ?></li>
                                <li><strong>Document Root:</strong> <small><?= $systemInfo['document_root'] ?></small></li>
                            </ul>

                            <h6 class="text-success mt-3">Software Versions</h6>
                            <ul class="list-unstyled small">
                                <li><strong>PHP:</strong> <?= $systemInfo['php_version'] ?></li>
                                <li><strong>MySQL:</strong> <?= $systemInfo['mysql_version'] ?></li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-warning">Performance Metrics</h6>
                            <div class="mb-2">
                                <small>Disk Usage</small>
                                <div class="progress" style="height: 10px;">
                                    <div class="progress-bar bg-<?= $diskUsedPercent > 90 ? 'danger' : ($diskUsedPercent > 70 ? 'warning' : 'success') ?>"
                                         style="width: <?= $diskUsedPercent ?>%"></div>
                                </div>
                                <small><?= $diskUsedPercent ?>% (<?= formatBytes($diskUsed) ?> / <?= formatBytes($diskTotal) ?>)</small>
                            </div>

                            <div class="mb-2">
                                <small>Memory Usage</small>
                                <div class="progress" style="height: 10px;">
                                    <div class="progress-bar bg-<?= $memoryUsagePercent > 80 ? 'danger' : ($memoryUsagePercent > 60 ? 'warning' : 'info') ?>"
                                         style="width: <?= $memoryUsagePercent ?>%"></div>
                                </div>
                                <small><?= $memoryUsagePercent ?>% (<?= formatBytes($memoryUsage) ?> / <?= $memoryLimit ?>)</small>
                            </div>

                            <?php if ($systemInfo['load'] !== 'N/A (Windows)'): ?>
                            <div class="mb-2">
                                <small>Server Load</small>
                                <div class="progress" style="height: 10px;">
                                    <div class="progress-bar bg-info" style="width: <?= min($systemInfo['load'][0] * 20, 100) ?>%"></div>
                                </div>
                                <small><?= number_format($systemInfo['load'][0], 2) ?></small>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <hr>

                    <h6 class="text-secondary">🔌 PHP Extensions</h6>
                    <div class="row">
                        <?php foreach ($systemInfo['extensions'] as $ext => $loaded): ?>
                        <div class="col-md-6">
                            <span class="badge bg-<?= $loaded ? 'success' : 'danger' ?> me-1">
                                <?= $loaded ? '✓' : '✗' ?> <?= $ext ?>
                            </span>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-6">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">🔒 Security & Health Check</h6>
                </div>
                <div class="card-body">
                    <h6 class="text-danger">Security Settings</h6>
                    <div class="row">
                        <?php foreach ($securityChecks as $check => $status): ?>
                        <div class="col-md-6 mb-2">
                            <span class="badge bg-<?= $status ? 'success' : 'warning' ?> w-100">
                                <?= $status ? '✓' : '⚠' ?> <?= ucfirst(str_replace('_', ' ', $check)) ?>
                            </span>
                        </div>
                        <?php endforeach; ?>
                    </div>

                    <h6 class="text-info mt-3">File Permissions</h6>
                    <div class="row">
                        <?php foreach ($filePermissions as $check => $status): ?>
                        <div class="col-md-6 mb-2">
                            <span class="badge bg-<?= $status ? 'success' : 'danger' ?> w-100">
                                <?= $status ? '✓' : '✗' ?> <?= ucfirst(str_replace('_', ' ', $check)) ?>
                            </span>
                        </div>
                        <?php endforeach; ?>
                    </div>

                    <hr>

                    <h6 class="text-success">Database Tables</h6>
                    <div class="table-responsive" style="max-height: 200px;">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Table</th>
                                    <th>Rows</th>
                                    <th>Size</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach (array_slice($databaseTables, 0, 8) as $table): ?>
                                <tr>
                                    <td><small><?= $table['table_name'] ?></small></td>
                                    <td><small><?= number_format($table['table_rows']) ?></small></td>
                                    <td><small><?= $table['size_mb'] ?> MB</small></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activities & Users -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">📋 Recent Activities</h6>
                    <a href="logs.php" class="btn btn-sm btn-primary">View All</a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Time</th>
                                    <th>User</th>
                                    <th>Activity</th>
                                    <th>IP</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recentActivities as $activity): ?>
                                <tr>
                                    <td>
                                        <small><?= date('H:i:s', strtotime($activity['created_at'])) ?></small><br>
                                        <small class="text-muted"><?= date('M d', strtotime($activity['created_at'])) ?></small>
                                    </td>
                                    <td>
                                        <strong><?= htmlspecialchars($activity['user_nama'] ?? 'System') ?></strong><br>
                                        <small class="text-muted"><?= htmlspecialchars($activity['user_email'] ?? '') ?></small>
                                    </td>
                                    <td><?= htmlspecialchars($activity['aktivitas'] ?? $activity['activity'] ?? 'No activity') ?></td>
                                    <td><small><?= htmlspecialchars($activity['ip_address'] ?? 'N/A') ?></small></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">👥 Recent Users</h6>
                </div>
                <div class="card-body">
                    <?php foreach ($recentUsers as $user): ?>
                    <div class="d-flex align-items-center mb-3">
                        <div class="avatar me-3">
                            <div class="avatar-initial bg-<?= $user['role'] === 'admin' ? 'danger' : 'primary' ?> rounded-circle">
                                <?= strtoupper(substr($user['nama'], 0, 1)) ?>
                            </div>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-0"><?= htmlspecialchars($user['nama']) ?></h6>
                            <small class="text-muted"><?= htmlspecialchars($user['email']) ?></small><br>
                            <span class="badge bg-<?= $user['role'] === 'admin' ? 'danger' : 'primary' ?> badge-sm">
                                <?= ucfirst($user['role']) ?>
                            </span>
                            <small class="text-muted ms-2"><?= date('M d, Y', strtotime($user['created_at'])) ?></small>
                        </div>
                    </div>
                    <?php endforeach; ?>

                    <div class="text-center mt-3">
                        <a href="users.php" class="btn btn-sm btn-outline-primary">View All Users</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Transactions -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">💰 Recent Transactions</h6>
            <a href="transaksi.php" class="btn btn-sm btn-primary">View All</a>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>User</th>
                            <th>Description</th>
                            <th>Category</th>
                            <th>Type</th>
                            <th>Amount</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($recentTransactions as $transaction): ?>
                        <tr>
                            <td>
                                <small><?= date('M d, Y', strtotime($transaction['tanggal'])) ?></small><br>
                                <small class="text-muted"><?= date('H:i', strtotime($transaction['created_at'])) ?></small>
                            </td>
                            <td><?= htmlspecialchars($transaction['user_nama'] ?? 'Unknown') ?></td>
                            <td><?= htmlspecialchars($transaction['keterangan'] ?? 'No description') ?></td>
                            <td>
                                <span class="badge bg-secondary"><?= htmlspecialchars($transaction['kategori_nama'] ?? 'No Category') ?></span>
                            </td>
                            <td>
                                <?php
                                $transactionType = $transaction['jenis'] ?? $transaction['kategori_tipe'] ?? 'unknown';
                                ?>
                                <span class="badge bg-<?= $transactionType === 'pemasukan' ? 'success' : 'danger' ?>">
                                    <?= ucfirst($transactionType) ?>
                                </span>
                            </td>
                            <td>
                                <strong class="text-<?= $transactionType === 'pemasukan' ? 'success' : 'danger' ?>">
                                    <?= $transactionType === 'pemasukan' ? '+' : '-' ?>Rp <?= number_format($transaction['jumlah']) ?>
                                </strong>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

<script>
// Admin Dashboard Functions
function clearCache() {
    if (confirm('Are you sure you want to clear the cache?')) {
        submitAdminAction('clear_cache');
    }
}

function optimizeDatabase() {
    if (confirm('Are you sure you want to optimize the database?')) {
        submitAdminAction('optimize_database');
    }
}

function backupDatabase() {
    if (confirm('Are you sure you want to create a database backup?')) {
        submitAdminAction('backup_database');
    }
}

function sendTestNotification() {
    if (confirm('Send test notification to all admins?')) {
        submitAdminAction('send_test_notification');
    }
}

function submitAdminAction(action) {
    const form = document.createElement('form');
    form.method = 'POST';
    form.innerHTML = `<input type="hidden" name="action" value="${action}">`;
    document.body.appendChild(form);

    if (window.showNotification) {
        window.showNotification({
            type: 'info',
            title: 'Processing',
            message: `Executing ${action.replace('_', ' ')}...`,
            duration: 3000
        });
    }

    form.submit();
}

function runSystemCheck() {
    if (window.showNotification) {
        window.showNotification({
            type: 'info',
            title: 'System Check',
            message: 'Running comprehensive system check...',
            duration: 3000
        });
    }

    // Simulate system check
    setTimeout(() => {
        if (window.showNotification) {
            window.showNotification({
                type: 'success',
                title: 'System Check Complete',
                message: 'All systems are running normally.',
                duration: 5000
            });
        }
    }, 3000);
}

// Auto-refresh stats every 30 seconds
setInterval(() => {
    // In a real implementation, this would fetch updated stats via AJAX
    console.log('Refreshing admin stats...');
}, 30000);

// Enhanced Admin Dashboard Functions
function openUserManagement() {
    if (window.showNotification) {
        window.showNotification({
            type: 'info',
            title: 'User Management',
            message: 'Opening user management panel...',
            duration: 3000
        });
    }
    setTimeout(() => {
        window.location.href = 'users.php';
    }, 500);
}

function openSystemMonitor() {
    if (window.showNotification) {
        window.showNotification({
            type: 'info',
            title: 'System Monitor',
            message: 'Opening system monitoring dashboard...',
            duration: 3000
        });
    }

    // Create a modal for system monitor
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">🖥️ System Monitor</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Real-time Metrics</h6>
                            <div id="systemMetrics">
                                <p>Loading system metrics...</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>Performance Charts</h6>
                            <div id="performanceCharts">
                                <p>Loading performance data...</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" onclick="refreshSystemData()">Refresh</button>
                </div>
            </div>
        </div>
    `;
    document.body.appendChild(modal);

    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();

    // Remove modal when hidden
    modal.addEventListener('hidden.bs.modal', () => {
        document.body.removeChild(modal);
    });
}

function refreshSystemData() {
    if (window.showNotification) {
        window.showNotification({
            type: 'info',
            title: 'Refreshing Data',
            message: 'Updating system information...',
            duration: 3000
        });
    }

    setTimeout(() => {
        location.reload();
    }, 1000);
}

// Format bytes helper function
function formatBytes(bytes, decimals = 2) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

// Enhanced dashboard initialization
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Auto-refresh dashboard every 5 minutes
    setInterval(() => {
        console.log('Auto-refreshing admin dashboard...');
        // In real implementation, this would update stats via AJAX
    }, 300000);

    // Add click animations to stat cards
    document.querySelectorAll('.enhanced-stats-card').forEach(card => {
        card.addEventListener('click', function() {
            this.style.transform = 'scale(0.98)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 150);
        });
    });
});
</script>

<script src="assets/js/enhanced-modern.js?v=<?= time() ?>"></script>

                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/views/layouts/footer.php'; ?>