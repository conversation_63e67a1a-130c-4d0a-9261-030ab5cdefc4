<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';
require_once 'includes/helpers/notifications.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isLoggedIn()) {
    setFlashMessage('danger', 'Silakan login terlebih dahulu');
    redirect('/keuangan/login.php');
}

$currentUser = getCurrentUser();
$currentPage = 'pembelian';

// Create pembelian table if not exists
try {
    $pdo->exec("CREATE TABLE IF NOT EXISTS pembelian (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        nomor_pembelian VARCHAR(50) UNIQUE NOT NULL,
        supplier_id INT,
        tanggal_pembelian DATE NOT NULL,
        total_pembelian DECIMAL(15,2) NOT NULL,
        status ENUM('pending', 'diterima', 'dibatalkan') DEFAULT 'pending',
        keterangan TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )");

    $pdo->exec("CREATE TABLE IF NOT EXISTS detail_pembelian (
        id INT AUTO_INCREMENT PRIMARY KEY,
        pembelian_id INT NOT NULL,
        produk_id INT,
        nama_produk VARCHAR(255) NOT NULL,
        qty INT NOT NULL,
        harga_beli DECIMAL(15,2) NOT NULL,
        subtotal DECIMAL(15,2) NOT NULL,
        FOREIGN KEY (pembelian_id) REFERENCES pembelian(id) ON DELETE CASCADE
    )");

    $pdo->exec("CREATE TABLE IF NOT EXISTS supplier (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        nama_supplier VARCHAR(255) NOT NULL,
        kontak VARCHAR(100),
        alamat TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )");
} catch (PDOException $e) {
    error_log("Error creating pembelian tables: " . $e->getMessage());
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        try {
            switch ($_POST['action']) {
                case 'add':
                    // Validasi input
                    $errors = [];
                    
                    if (empty($_POST['tanggal_pembelian'])) {
                        $errors[] = 'Tanggal pembelian harus diisi';
                    }
                    
                    if (empty($_POST['total_pembelian'])) {
                        $errors[] = 'Total pembelian harus diisi';
                    }
                    
                    if (empty($errors)) {
                        try {
                            // Generate nomor pembelian
                            $nomor_pembelian = 'PB' . date('Ymd') . sprintf('%04d', rand(1, 9999));
                            
                            // Format jumlah (hapus format angka)
                            $total = str_replace(['.', ','], '', $_POST['total_pembelian']);
                            
                            // Format tanggal ke format Y-m-d
                            $tanggal = date('Y-m-d', strtotime($_POST['tanggal_pembelian']));
                            
                            // Insert pembelian dengan prepared statement
                            $sql = "INSERT INTO pembelian (user_id, nomor_pembelian, supplier_id, tanggal_pembelian, total_pembelian, status, keterangan, created_at) 
                                   VALUES (:user_id, :nomor_pembelian, :supplier_id, :tanggal_pembelian, :total_pembelian, :status, :keterangan, NOW())";
                            
                            $stmt = $pdo->prepare($sql);
                            
                            $params = [
                                ':user_id' => $currentUser['id'],
                                ':nomor_pembelian' => $nomor_pembelian,
                                ':supplier_id' => !empty($_POST['supplier_id']) ? $_POST['supplier_id'] : null,
                                ':tanggal_pembelian' => $tanggal,
                                ':total_pembelian' => $total,
                                ':status' => $_POST['status'] ?? 'pending',
                                ':keterangan' => $_POST['keterangan']
                            ];
                            
                            $result = $stmt->execute($params);
                            
                            if ($result) {
                                // Log aktivitas
                                logActivity($currentUser['id'], sprintf(
                                    'Menambahkan pembelian %s sebesar %s',
                                    $nomor_pembelian,
                                    formatRupiah($total)
                                ));
                                
                                setFlashMessage('success', 'Pembelian berhasil ditambahkan');
                                redirect('/keuangan/pembelian.php');
                            } else {
                                throw new Exception('Gagal menyimpan pembelian');
                            }
                        } catch (Exception $e) {
                            setFlashMessage('danger', $e->getMessage());
                        }
                    } else {
                        setFlashMessage('danger', implode('<br>', $errors));
                    }
                    break;

                case 'update':
                    if (empty($_POST['id'])) {
                        setFlashMessage('danger', 'ID pembelian tidak valid');
                        break;
                    }

                    // Format jumlah (hapus format angka)
                    $total = str_replace(['.', ','], '', $_POST['total_pembelian']);
                    
                    // Format tanggal ke format Y-m-d
                    $tanggal = date('Y-m-d', strtotime($_POST['tanggal_pembelian']));
                    
                    $stmt = $pdo->prepare("
                        UPDATE pembelian 
                        SET supplier_id = ?, tanggal_pembelian = ?, total_pembelian = ?, status = ?, keterangan = ?
                        WHERE id = ? AND user_id = ?
                    ");
                    
                    $result = $stmt->execute([
                        !empty($_POST['supplier_id']) ? $_POST['supplier_id'] : null,
                        $tanggal,
                        $total,
                        $_POST['status'],
                        $_POST['keterangan'],
                        $_POST['id'],
                        $currentUser['id']
                    ]);

                    if ($result) {
                        setFlashMessage('success', 'Pembelian berhasil diperbarui');
                    } else {
                        setFlashMessage('danger', 'Gagal memperbarui pembelian');
                    }
                    break;

                case 'delete':
                    if (empty($_POST['id'])) {
                        setFlashMessage('danger', 'ID pembelian tidak valid');
                        break;
                    }

                    $stmt = $pdo->prepare("DELETE FROM pembelian WHERE id = ? AND user_id = ?");
                    $result = $stmt->execute([$_POST['id'], $currentUser['id']]);

                    if ($result) {
                        setFlashMessage('success', 'Pembelian berhasil dihapus');
                    } else {
                        setFlashMessage('danger', 'Gagal menghapus pembelian');
                    }
                    break;
            }
        } catch (PDOException $e) {
            setFlashMessage('danger', 'Terjadi kesalahan. Silakan coba lagi.');
        }
        redirect('/keuangan/pembelian.php');
    }
}

// Get purchases with pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$perPage = 10;
$offset = ($page - 1) * $perPage;

// Get total records with filters
$where = ["p.user_id = ?"];
$params = [$currentUser['id']];

if (!empty($_GET['status'])) {
    $where[] = "p.status = ?";
    $params[] = $_GET['status'];
}

if (!empty($_GET['supplier_id'])) {
    $where[] = "p.supplier_id = ?";
    $params[] = $_GET['supplier_id'];
}

if (!empty($_GET['start_date'])) {
    $where[] = "p.tanggal_pembelian >= ?";
    $params[] = $_GET['start_date'];
}

if (!empty($_GET['end_date'])) {
    $where[] = "p.tanggal_pembelian <= ?";
    $params[] = $_GET['end_date'];
}

$whereClause = implode(" AND ", $where);

// Get total records
$stmt = $pdo->prepare("
    SELECT COUNT(*) as total 
    FROM pembelian p
    WHERE $whereClause
");
$stmt->execute($params);
$totalRecords = $stmt->fetch()['total'];
$totalPages = ceil($totalRecords / $perPage);

// Get purchases with supplier information
$stmt = $pdo->prepare("
    SELECT 
        p.*,
        s.nama_supplier
    FROM pembelian p
    LEFT JOIN supplier s ON p.supplier_id = s.id
    WHERE $whereClause
    ORDER BY p.tanggal_pembelian DESC, p.created_at DESC
    LIMIT ? OFFSET ?
");

// Create new array for pagination parameters
$paginationParams = $params;
$paginationParams[] = $perPage;
$paginationParams[] = $offset;

$stmt->execute($paginationParams);
$pembelian = $stmt->fetchAll();

// Get suppliers for filter and form
$stmt = $pdo->prepare("
    SELECT * FROM supplier 
    WHERE user_id = ? 
    ORDER BY nama_supplier ASC
");
$stmt->execute([$currentUser['id']]);
$suppliers = $stmt->fetchAll();

// Get statistics
$stmt = $pdo->prepare("
    SELECT 
        SUM(CASE WHEN status = 'pending' THEN total_pembelian ELSE 0 END) as total_pending,
        SUM(CASE WHEN status = 'diterima' THEN total_pembelian ELSE 0 END) as total_diterima,
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as count_pending,
        COUNT(CASE WHEN status = 'diterima' THEN 1 END) as count_diterima
    FROM pembelian
    WHERE user_id = ?
");
$stmt->execute([$currentUser['id']]);
$stats = $stmt->fetch();

// Include header
require_once 'includes/views/layouts/modern_page_template.php';
include 'includes/views/layouts/header.php';
?>

<div class="content-wrapper">
    <div class="container-fluid">
        <div class="row justify-content-center">
            <div class="col-12 col-xl-11 col-xxl-10">
                <div class="main-content-container">

                    <?php
                    // Page Header
                    renderModernPageHeader(
                        'Manajemen Pembelian',
                        'Kelola pembelian barang, tracking supplier, dan monitoring cash flow bisnis',
                        [
                            ['label' => 'Home', 'url' => '/keuangan/dashboard.php', 'icon' => 'fas fa-home'],
                            ['label' => 'Bisnis', 'url' => '#', 'icon' => 'fas fa-store'],
                            ['label' => 'Pembelian', 'url' => '/keuangan/pembelian.php', 'icon' => 'fas fa-shopping-basket']
                        ],
                        [
                            [
                                'type' => 'button',
                                'label' => 'Tambah Pembelian',
                                'icon' => 'fas fa-plus',
                                'class' => 'btn-primary',
                                'data-bs-toggle' => 'modal',
                                'data-bs-target' => '#addPurchaseModal'
                            ]
                        ]
                    );
                    ?>

                    <!-- Statistics Cards -->
                    <?php
                    $totalPembelian = ($stats['total_pending'] ?? 0) + ($stats['total_diterima'] ?? 0);
                    $totalTransaksi = ($stats['count_pending'] ?? 0) + ($stats['count_diterima'] ?? 0);
                    $avgPerTransaksi = $totalTransaksi > 0 ? $totalPembelian / $totalTransaksi : 0;

                    $statsCards = [
                        [
                            'label' => 'Total Pending',
                            'value' => formatRupiah($stats['total_pending'] ?? 0),
                            'icon' => 'fas fa-clock',
                            'color' => 'warning',
                            'subtitle' => ($stats['count_pending'] ?? 0) . ' transaksi menunggu'
                        ],
                        [
                            'label' => 'Total Diterima',
                            'value' => formatRupiah($stats['total_diterima'] ?? 0),
                            'icon' => 'fas fa-check-circle',
                            'color' => 'success',
                            'subtitle' => ($stats['count_diterima'] ?? 0) . ' transaksi selesai'
                        ],
                        [
                            'label' => 'Total Pembelian',
                            'value' => formatRupiah($totalPembelian),
                            'icon' => 'fas fa-shopping-basket',
                            'color' => 'primary',
                            'subtitle' => $totalTransaksi . ' total transaksi'
                        ],
                        [
                            'label' => 'Rata-rata Pembelian',
                            'value' => formatRupiah($avgPerTransaksi),
                            'icon' => 'fas fa-calculator',
                            'color' => 'info',
                            'subtitle' => count($suppliers) . ' supplier aktif'
                        ]
                    ];

                    renderModernStatsCards($statsCards);
                    ?>

    <!-- Filter Section -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-body">
            <form action="" method="GET" class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">Tanggal Mulai</label>
                    <input type="date" name="start_date" class="form-control" value="<?= $_GET['start_date'] ?? '' ?>">
                </div>
                <div class="col-md-3">
                    <label class="form-label">Tanggal Akhir</label>
                    <input type="date" name="end_date" class="form-control" value="<?= $_GET['end_date'] ?? '' ?>">
                </div>
                <div class="col-md-3">
                    <label class="form-label">Supplier</label>
                    <select name="supplier_id" class="form-select">
                        <option value="">Semua Supplier</option>
                        <?php foreach ($suppliers as $supplier): ?>
                        <option value="<?= $supplier['id'] ?>" <?= (isset($_GET['supplier_id']) && $_GET['supplier_id'] == $supplier['id']) ? 'selected' : '' ?>>
                            <?= htmlspecialchars($supplier['nama_supplier']) ?>
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">Status</label>
                    <select name="status" class="form-select">
                        <option value="">Semua Status</option>
                        <option value="pending" <?= (isset($_GET['status']) && $_GET['status'] == 'pending') ? 'selected' : '' ?>>Pending</option>
                        <option value="diterima" <?= (isset($_GET['status']) && $_GET['status'] == 'diterima') ? 'selected' : '' ?>>Diterima</option>
                        <option value="dibatalkan" <?= (isset($_GET['status']) && $_GET['status'] == 'dibatalkan') ? 'selected' : '' ?>>Dibatalkan</option>
                    </select>
                </div>
                <div class="col-12 text-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter me-2"></i>Filter
                    </button>
                    <a href="/keuangan/pembelian.php" class="btn btn-light">
                        <i class="fas fa-sync me-2"></i>Reset
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Purchases Table -->
    <div class="card border-0 shadow-sm">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover align-middle">
                    <thead>
                        <tr>
                            <th>Nomor Pembelian</th>
                            <th>Tanggal</th>
                            <th>Supplier</th>
                            <th class="text-end">Total</th>
                            <th class="text-center">Status</th>
                            <th class="text-center">Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($pembelian)): ?>
                        <tr>
                            <td colspan="6" class="text-center py-5">
                                <div class="text-muted">
                                    <i class="fas fa-shopping-basket fa-3x mb-3"></i>
                                    <p class="mb-0">Belum ada data pembelian</p>
                                </div>
                            </td>
                        </tr>
                        <?php else: ?>
                        <?php foreach ($pembelian as $item): ?>
                        <tr>
                            <td>
                                <div class="fw-bold"><?= htmlspecialchars($item['nomor_pembelian']) ?></div>
                                <?php if ($item['keterangan']): ?>
                                    <small class="text-muted"><?= htmlspecialchars($item['keterangan']) ?></small>
                                <?php endif; ?>
                            </td>
                            <td><?= formatTanggal($item['tanggal_pembelian']) ?></td>
                            <td>
                                <?php if ($item['nama_supplier']): ?>
                                    <?= htmlspecialchars($item['nama_supplier']) ?>
                                <?php else: ?>
                                    <span class="text-muted">-</span>
                                <?php endif; ?>
                            </td>
                            <td class="text-end fw-bold"><?= formatRupiah($item['total_pembelian']) ?></td>
                            <td class="text-center">
                                <span class="badge bg-<?= $item['status'] === 'diterima' ? 'success' : ($item['status'] === 'dibatalkan' ? 'danger' : 'warning') ?>">
                                    <?= ucfirst($item['status']) ?>
                                </span>
                            </td>
                            <td class="text-center">
                                <div class="btn-group">
                                    <button type="button" class="btn btn-sm btn-light" onclick="editPurchase(
                                        '<?= $item['id'] ?>',
                                        '<?= $item['supplier_id'] ?>',
                                        '<?= date('Y-m-d', strtotime($item['tanggal_pembelian'])) ?>',
                                        '<?= $item['total_pembelian'] ?>',
                                        '<?= $item['status'] ?>',
                                        '<?= htmlspecialchars($item['keterangan']) ?>'
                                    )">
                                        <i class="fas fa-edit text-primary"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-light" onclick="deletePurchase(<?= $item['id'] ?>)">
                                        <i class="fas fa-trash text-danger"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if ($totalPages > 1): ?>
            <nav class="mt-4">
                <ul class="pagination justify-content-center">
                    <?php if ($page > 1): ?>
                    <li class="page-item">
                        <a class="page-link" href="?page=<?= $page - 1 ?><?= http_build_query(array_filter($_GET)) ?>">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    </li>
                    <?php endif; ?>

                    <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                    <li class="page-item <?= $i === $page ? 'active' : '' ?>">
                        <a class="page-link" href="?page=<?= $i ?><?= http_build_query(array_filter($_GET)) ?>"><?= $i ?></a>
                    </li>
                    <?php endfor; ?>

                    <?php if ($page < $totalPages): ?>
                    <li class="page-item">
                        <a class="page-link" href="?page=<?= $page + 1 ?><?= http_build_query(array_filter($_GET)) ?>">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
            </nav>
            <?php endif; ?>
        </div>
    </div>
</div>
