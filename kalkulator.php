<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';
require_once 'includes/helpers/notifications.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isLoggedIn()) {
    setFlashMessage('danger', 'Silakan login terlebih dahulu');
    redirect('/keuangan/login.php');
}

$currentUser = getCurrentUser();
$currentPage = 'kalkulator';

// Include header
require_once 'includes/views/layouts/enhanced_modern_template.php';
include 'includes/views/layouts/header.php';
?>

<link href="assets/css/enhanced-modern.css?v=<?= time() ?>" rel="stylesheet">

<div class="content-wrapper">
    <div class="container-fluid">
        <div class="row justify-content-center">
            <div class="col-12 col-xl-11 col-xxl-10">
                <div class="main-content-container">

                    <?php
                    // Enhanced Page Header
                    renderEnhancedPageHeader(
                        'Kalkulator Keuangan',
                        'Tools kalkulator untuk berbagai perhitungan keuangan dan simulasi investasi dengan akurasi tinggi',
                        [
                            ['label' => 'Home', 'url' => '/keuangan/dashboard.php', 'icon' => 'fas fa-home'],
                            ['label' => 'Tools', 'url' => '#', 'icon' => 'fas fa-tools'],
                            ['label' => 'Kalkulator', 'url' => '/keuangan/kalkulator.php', 'icon' => 'fas fa-calculator']
                        ],
                        [
                            [
                                'type' => 'button',
                                'label' => 'Reset All',
                                'icon' => 'fas fa-redo',
                                'class' => 'btn-outline-warning',
                                'onclick' => 'resetAllCalculators()'
                            ],
                            [
                                'type' => 'button',
                                'label' => 'Save Results',
                                'icon' => 'fas fa-save',
                                'class' => 'btn-success',
                                'onclick' => 'saveCalculationResults()'
                            ]
                        ],
                        [
                            'icon' => 'fas fa-calculator',
                            'search' => false
                        ]
                    );
                    ?>

    <div class="row g-4">
        <!-- Basic Calculator -->
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-calculator me-2"></i>Kalkulator Dasar</h5>
                </div>
                <div class="card-body">
                    <div class="calculator">
                        <div class="calculator-display">
                            <input type="text" id="display" class="form-control form-control-lg text-end" readonly value="0">
                        </div>
                        <div class="calculator-buttons mt-3">
                            <div class="row g-2">
                                <div class="col-3">
                                    <button class="btn btn-light w-100" onclick="clearDisplay()">C</button>
                                </div>
                                <div class="col-3">
                                    <button class="btn btn-light w-100" onclick="deleteLast()">⌫</button>
                                </div>
                                <div class="col-3">
                                    <button class="btn btn-light w-100" onclick="appendToDisplay('/')">/</button>
                                </div>
                                <div class="col-3">
                                    <button class="btn btn-light w-100" onclick="appendToDisplay('*')">×</button>
                                </div>
                            </div>
                            <div class="row g-2 mt-1">
                                <div class="col-3">
                                    <button class="btn btn-outline-primary w-100" onclick="appendToDisplay('7')">7</button>
                                </div>
                                <div class="col-3">
                                    <button class="btn btn-outline-primary w-100" onclick="appendToDisplay('8')">8</button>
                                </div>
                                <div class="col-3">
                                    <button class="btn btn-outline-primary w-100" onclick="appendToDisplay('9')">9</button>
                                </div>
                                <div class="col-3">
                                    <button class="btn btn-light w-100" onclick="appendToDisplay('-')">-</button>
                                </div>
                            </div>
                            <div class="row g-2 mt-1">
                                <div class="col-3">
                                    <button class="btn btn-outline-primary w-100" onclick="appendToDisplay('4')">4</button>
                                </div>
                                <div class="col-3">
                                    <button class="btn btn-outline-primary w-100" onclick="appendToDisplay('5')">5</button>
                                </div>
                                <div class="col-3">
                                    <button class="btn btn-outline-primary w-100" onclick="appendToDisplay('6')">6</button>
                                </div>
                                <div class="col-3">
                                    <button class="btn btn-light w-100" onclick="appendToDisplay('+')">+</button>
                                </div>
                            </div>
                            <div class="row g-2 mt-1">
                                <div class="col-3">
                                    <button class="btn btn-outline-primary w-100" onclick="appendToDisplay('1')">1</button>
                                </div>
                                <div class="col-3">
                                    <button class="btn btn-outline-primary w-100" onclick="appendToDisplay('2')">2</button>
                                </div>
                                <div class="col-3">
                                    <button class="btn btn-outline-primary w-100" onclick="appendToDisplay('3')">3</button>
                                </div>
                                <div class="col-3 row-span-2">
                                    <button class="btn btn-success w-100 h-100" onclick="calculate()" style="height: 80px;">=</button>
                                </div>
                            </div>
                            <div class="row g-2 mt-1">
                                <div class="col-6">
                                    <button class="btn btn-outline-primary w-100" onclick="appendToDisplay('0')">0</button>
                                </div>
                                <div class="col-3">
                                    <button class="btn btn-outline-primary w-100" onclick="appendToDisplay('.')">.</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Loan Calculator -->
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0"><i class="fas fa-home me-2"></i>Kalkulator Kredit</h5>
                </div>
                <div class="card-body">
                    <form id="loanForm">
                        <div class="mb-3">
                            <label class="form-label">Jumlah Pinjaman</label>
                            <div class="input-group">
                                <span class="input-group-text">Rp</span>
                                <input type="text" id="loanAmount" class="form-control number-format" placeholder="100,000,000">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Suku Bunga (% per tahun)</label>
                            <input type="number" id="interestRate" class="form-control" placeholder="12" step="0.1">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Jangka Waktu (tahun)</label>
                            <input type="number" id="loanTerm" class="form-control" placeholder="10">
                        </div>
                        <button type="button" class="btn btn-success w-100" onclick="calculateLoan()">
                            <i class="fas fa-calculator me-2"></i>Hitung Cicilan
                        </button>
                    </form>
                    
                    <div id="loanResult" class="mt-4" style="display: none;">
                        <div class="alert alert-success">
                            <h6 class="mb-2">Hasil Perhitungan:</h6>
                            <div class="row">
                                <div class="col-6">
                                    <small class="text-muted">Cicilan per Bulan:</small>
                                    <div class="fw-bold" id="monthlyPayment">-</div>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted">Total Pembayaran:</small>
                                    <div class="fw-bold" id="totalPayment">-</div>
                                </div>
                            </div>
                            <div class="mt-2">
                                <small class="text-muted">Total Bunga:</small>
                                <div class="fw-bold" id="totalInterest">-</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Investment Calculator -->
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Kalkulator Investasi</h5>
                </div>
                <div class="card-body">
                    <form id="investmentForm">
                        <div class="mb-3">
                            <label class="form-label">Investasi Awal</label>
                            <div class="input-group">
                                <span class="input-group-text">Rp</span>
                                <input type="text" id="initialInvestment" class="form-control number-format" placeholder="10,000,000">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Investasi Bulanan</label>
                            <div class="input-group">
                                <span class="input-group-text">Rp</span>
                                <input type="text" id="monthlyInvestment" class="form-control number-format" placeholder="1,000,000">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Return Tahunan (%)</label>
                            <input type="number" id="annualReturn" class="form-control" placeholder="12" step="0.1">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Jangka Waktu (tahun)</label>
                            <input type="number" id="investmentTerm" class="form-control" placeholder="10">
                        </div>
                        <button type="button" class="btn btn-info w-100" onclick="calculateInvestment()">
                            <i class="fas fa-calculator me-2"></i>Hitung Investasi
                        </button>
                    </form>
                    
                    <div id="investmentResult" class="mt-4" style="display: none;">
                        <div class="alert alert-info">
                            <h6 class="mb-2">Hasil Perhitungan:</h6>
                            <div class="row">
                                <div class="col-6">
                                    <small class="text-muted">Total Investasi:</small>
                                    <div class="fw-bold" id="totalInvested">-</div>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted">Nilai Akhir:</small>
                                    <div class="fw-bold" id="finalValue">-</div>
                                </div>
                            </div>
                            <div class="mt-2">
                                <small class="text-muted">Total Keuntungan:</small>
                                <div class="fw-bold" id="totalProfit">-</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Savings Calculator -->
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0"><i class="fas fa-piggy-bank me-2"></i>Kalkulator Tabungan</h5>
                </div>
                <div class="card-body">
                    <form id="savingsForm">
                        <div class="mb-3">
                            <label class="form-label">Target Tabungan</label>
                            <div class="input-group">
                                <span class="input-group-text">Rp</span>
                                <input type="text" id="savingsTarget" class="form-control number-format" placeholder="100,000,000">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Tabungan Awal</label>
                            <div class="input-group">
                                <span class="input-group-text">Rp</span>
                                <input type="text" id="initialSavings" class="form-control number-format" placeholder="5,000,000">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Tabungan per Bulan</label>
                            <div class="input-group">
                                <span class="input-group-text">Rp</span>
                                <input type="text" id="monthlySavings" class="form-control number-format" placeholder="2,000,000">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Bunga Tabungan (% per tahun)</label>
                            <input type="number" id="savingsInterest" class="form-control" placeholder="3" step="0.1">
                        </div>
                        <button type="button" class="btn btn-warning w-100" onclick="calculateSavings()">
                            <i class="fas fa-calculator me-2"></i>Hitung Waktu Target
                        </button>
                    </form>
                    
                    <div id="savingsResult" class="mt-4" style="display: none;">
                        <div class="alert alert-warning">
                            <h6 class="mb-2">Hasil Perhitungan:</h6>
                            <div class="row">
                                <div class="col-6">
                                    <small class="text-muted">Waktu Mencapai Target:</small>
                                    <div class="fw-bold" id="timeToTarget">-</div>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted">Total Bunga:</small>
                                    <div class="fw-bold" id="savingsInterestEarned">-</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Basic Calculator Functions
let display = document.getElementById('display');
let currentInput = '0';
let shouldResetDisplay = false;

function updateDisplay() {
    display.value = currentInput;
}

function clearDisplay() {
    currentInput = '0';
    updateDisplay();
}

function deleteLast() {
    if (currentInput.length > 1) {
        currentInput = currentInput.slice(0, -1);
    } else {
        currentInput = '0';
    }
    updateDisplay();
}

function appendToDisplay(value) {
    if (shouldResetDisplay) {
        currentInput = '0';
        shouldResetDisplay = false;
    }

    if (currentInput === '0' && value !== '.') {
        currentInput = value;
    } else {
        currentInput += value;
    }
    updateDisplay();
}

function calculate() {
    try {
        let result = eval(currentInput.replace(/×/g, '*'));
        currentInput = result.toString();
        shouldResetDisplay = true;
        updateDisplay();
    } catch (error) {
        currentInput = 'Error';
        shouldResetDisplay = true;
        updateDisplay();
    }
}

// Loan Calculator
function calculateLoan() {
    const loanAmount = parseFloat(document.getElementById('loanAmount').value.replace(/[^\d]/g, ''));
    const annualRate = parseFloat(document.getElementById('interestRate').value) / 100;
    const years = parseFloat(document.getElementById('loanTerm').value);

    if (!loanAmount || !annualRate || !years) {
        alert('Mohon isi semua field dengan benar');
        return;
    }

    const monthlyRate = annualRate / 12;
    const numPayments = years * 12;

    const monthlyPayment = loanAmount * (monthlyRate * Math.pow(1 + monthlyRate, numPayments)) /
                          (Math.pow(1 + monthlyRate, numPayments) - 1);

    const totalPayment = monthlyPayment * numPayments;
    const totalInterest = totalPayment - loanAmount;

    document.getElementById('monthlyPayment').textContent = formatRupiah(monthlyPayment);
    document.getElementById('totalPayment').textContent = formatRupiah(totalPayment);
    document.getElementById('totalInterest').textContent = formatRupiah(totalInterest);
    document.getElementById('loanResult').style.display = 'block';
}

// Investment Calculator
function calculateInvestment() {
    const initialInvestment = parseFloat(document.getElementById('initialInvestment').value.replace(/[^\d]/g, '')) || 0;
    const monthlyInvestment = parseFloat(document.getElementById('monthlyInvestment').value.replace(/[^\d]/g, '')) || 0;
    const annualReturn = parseFloat(document.getElementById('annualReturn').value) / 100;
    const years = parseFloat(document.getElementById('investmentTerm').value);

    if (!annualReturn || !years) {
        alert('Mohon isi semua field dengan benar');
        return;
    }

    const monthlyReturn = annualReturn / 12;
    const months = years * 12;

    // Future value of initial investment
    const futureValueInitial = initialInvestment * Math.pow(1 + monthlyReturn, months);

    // Future value of monthly investments (annuity)
    const futureValueMonthly = monthlyInvestment *
        ((Math.pow(1 + monthlyReturn, months) - 1) / monthlyReturn);

    const finalValue = futureValueInitial + futureValueMonthly;
    const totalInvested = initialInvestment + (monthlyInvestment * months);
    const totalProfit = finalValue - totalInvested;

    document.getElementById('totalInvested').textContent = formatRupiah(totalInvested);
    document.getElementById('finalValue').textContent = formatRupiah(finalValue);
    document.getElementById('totalProfit').textContent = formatRupiah(totalProfit);
    document.getElementById('investmentResult').style.display = 'block';
}

// Savings Calculator
function calculateSavings() {
    const target = parseFloat(document.getElementById('savingsTarget').value.replace(/[^\d]/g, ''));
    const initial = parseFloat(document.getElementById('initialSavings').value.replace(/[^\d]/g, '')) || 0;
    const monthly = parseFloat(document.getElementById('monthlySavings').value.replace(/[^\d]/g, ''));
    const annualInterest = parseFloat(document.getElementById('savingsInterest').value) / 100;

    if (!target || !monthly) {
        alert('Mohon isi target tabungan dan tabungan per bulan');
        return;
    }

    const monthlyInterest = annualInterest / 12;
    let currentAmount = initial;
    let months = 0;
    let totalInterest = 0;

    while (currentAmount < target && months < 1200) { // Max 100 years
        const interestEarned = currentAmount * monthlyInterest;
        totalInterest += interestEarned;
        currentAmount += monthly + interestEarned;
        months++;
    }

    const years = Math.floor(months / 12);
    const remainingMonths = months % 12;

    let timeText = '';
    if (years > 0) {
        timeText += years + ' tahun ';
    }
    if (remainingMonths > 0) {
        timeText += remainingMonths + ' bulan';
    }

    document.getElementById('timeToTarget').textContent = timeText || 'Target sudah tercapai';
    document.getElementById('savingsInterestEarned').textContent = formatRupiah(totalInterest);
    document.getElementById('savingsResult').style.display = 'block';
}

// Format number as Rupiah
function formatRupiah(amount) {
    return 'Rp ' + new Intl.NumberFormat('id-ID').format(Math.round(amount));
}

// Format number inputs
document.addEventListener('DOMContentLoaded', function() {
    const numberInputs = document.querySelectorAll('.number-format');

    numberInputs.forEach(function(input) {
        input.addEventListener('input', function(e) {
            let value = e.target.value.replace(/[^\d]/g, '');
            if (value) {
                e.target.value = new Intl.NumberFormat('id-ID').format(value);
            }
        });
    });
});

// Enhanced calculator functions
function resetAllCalculators() {
    // Reset basic calculator
    clearDisplay();

    // Reset loan calculator
    document.getElementById('loanForm').reset();
    document.getElementById('loanResult').style.display = 'none';

    // Reset investment calculator
    document.getElementById('investmentForm').reset();
    document.getElementById('investmentResult').style.display = 'none';

    // Reset savings calculator
    document.getElementById('savingsForm').reset();
    document.getElementById('savingsResult').style.display = 'none';

    if (window.showNotification) {
        window.showNotification({
            type: 'success',
            title: 'Reset Complete',
            message: 'All calculators have been reset',
            duration: 3000
        });
    }
}

function saveCalculationResults() {
    const results = {
        timestamp: new Date().toISOString(),
        loan: document.getElementById('loanResult').style.display !== 'none' ? {
            monthlyPayment: document.getElementById('monthlyPayment').textContent,
            totalPayment: document.getElementById('totalPayment').textContent,
            totalInterest: document.getElementById('totalInterest').textContent
        } : null,
        investment: document.getElementById('investmentResult').style.display !== 'none' ? {
            totalInvested: document.getElementById('totalInvested').textContent,
            finalValue: document.getElementById('finalValue').textContent,
            totalProfit: document.getElementById('totalProfit').textContent
        } : null,
        savings: document.getElementById('savingsResult').style.display !== 'none' ? {
            timeToTarget: document.getElementById('timeToTarget').textContent,
            savingsInterestEarned: document.getElementById('savingsInterestEarned').textContent
        } : null
    };

    localStorage.setItem('calculatorResults', JSON.stringify(results));

    if (window.showNotification) {
        window.showNotification({
            type: 'success',
            title: 'Results Saved',
            message: 'Calculation results have been saved locally',
            duration: 3000
        });
    }
}
</script>

<script src="assets/js/enhanced-modern.js?v=<?= time() ?>"></script>

                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/views/layouts/footer.php'; ?>
