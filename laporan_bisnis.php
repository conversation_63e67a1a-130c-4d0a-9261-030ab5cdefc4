<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';
require_once 'includes/helpers/notifications.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isLoggedIn()) {
    setFlashMessage('danger', 'Silakan login terlebih dahulu');
    redirect('/keuangan/login.php');
}

$currentUser = getCurrentUser();
$currentPage = 'laporan_bisnis';

// Get date range from request
$startDate = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-01');
$endDate = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-t');

// Validate dates
if (!$startDate || !$endDate) {
    $startDate = date('Y-m-01');
    $endDate = date('Y-m-t');
}

try {
    // Get sales summary
    $stmt = $pdo->prepare("
        SELECT 
            COUNT(*) as total_penjualan,
            SUM(total_penjualan) as total_nilai_penjualan,
            AVG(total_penjualan) as rata_rata_penjualan
        FROM penjualan 
        WHERE user_id = ? AND tanggal_penjualan BETWEEN ? AND ?
    ");
    $stmt->execute([$currentUser['id'], $startDate, $endDate]);
    $salesSummary = $stmt->fetch() ?: ['total_penjualan' => 0, 'total_nilai_penjualan' => 0, 'rata_rata_penjualan' => 0];

    // Get purchase summary
    $stmt = $pdo->prepare("
        SELECT 
            COUNT(*) as total_pembelian,
            SUM(total_pembelian) as total_nilai_pembelian,
            AVG(total_pembelian) as rata_rata_pembelian
        FROM pembelian 
        WHERE user_id = ? AND tanggal_pembelian BETWEEN ? AND ?
    ");
    $stmt->execute([$currentUser['id'], $startDate, $endDate]);
    $purchaseSummary = $stmt->fetch() ?: ['total_pembelian' => 0, 'total_nilai_pembelian' => 0, 'rata_rata_pembelian' => 0];

    // Get top selling products
    $stmt = $pdo->prepare("
        SELECT 
            p.nama_produk,
            SUM(dp.qty) as total_qty,
            SUM(dp.subtotal) as total_nilai
        FROM detail_penjualan dp
        JOIN penjualan pj ON dp.penjualan_id = pj.id
        LEFT JOIN produk p ON dp.produk_id = p.id
        WHERE pj.user_id = ? AND pj.tanggal_penjualan BETWEEN ? AND ?
        GROUP BY dp.produk_id, dp.nama_produk
        ORDER BY total_qty DESC
        LIMIT 10
    ");
    $stmt->execute([$currentUser['id'], $startDate, $endDate]);
    $topProducts = $stmt->fetchAll();

    // Get monthly sales trend
    $stmt = $pdo->prepare("
        SELECT 
            DATE_FORMAT(tanggal_penjualan, '%Y-%m') as bulan,
            COUNT(*) as jumlah_transaksi,
            SUM(total_penjualan) as total_penjualan
        FROM penjualan 
        WHERE user_id = ? AND tanggal_penjualan BETWEEN ? AND ?
        GROUP BY DATE_FORMAT(tanggal_penjualan, '%Y-%m')
        ORDER BY bulan ASC
    ");
    $stmt->execute([$currentUser['id'], $startDate, $endDate]);
    $salesTrend = $stmt->fetchAll();

    // Get supplier performance
    $stmt = $pdo->prepare("
        SELECT 
            s.nama_supplier,
            COUNT(p.id) as total_pembelian,
            SUM(p.total_pembelian) as total_nilai
        FROM pembelian p
        LEFT JOIN supplier s ON p.supplier_id = s.id
        WHERE p.user_id = ? AND p.tanggal_pembelian BETWEEN ? AND ?
        GROUP BY p.supplier_id, s.nama_supplier
        ORDER BY total_nilai DESC
        LIMIT 10
    ");
    $stmt->execute([$currentUser['id'], $startDate, $endDate]);
    $supplierPerformance = $stmt->fetchAll();

} catch (PDOException $e) {
    $salesSummary = ['total_penjualan' => 0, 'total_nilai_penjualan' => 0, 'rata_rata_penjualan' => 0];
    $purchaseSummary = ['total_pembelian' => 0, 'total_nilai_pembelian' => 0, 'rata_rata_pembelian' => 0];
    $topProducts = [];
    $salesTrend = [];
    $supplierPerformance = [];
}

// Calculate profit
$grossProfit = ($salesSummary['total_nilai_penjualan'] ?? 0) - ($purchaseSummary['total_nilai_pembelian'] ?? 0);
$profitMargin = $salesSummary['total_nilai_penjualan'] > 0 ? ($grossProfit / $salesSummary['total_nilai_penjualan']) * 100 : 0;

// Include header
require_once 'includes/views/layouts/enhanced_modern_template.php';
include 'includes/views/layouts/header.php';
?>

<link href="assets/css/enhanced-modern.css?v=<?= time() ?>" rel="stylesheet">

<div class="content-wrapper">
    <div class="container-fluid">
        <div class="row justify-content-center">
            <div class="col-12 col-xl-11 col-xxl-10">
                <div class="main-content-container">

                    <?php
                    // Enhanced Page Header
                    renderEnhancedPageHeader(
                        'Laporan Bisnis Komprehensif',
                        'Analisis performa bisnis dan tren penjualan dengan insights actionable dan visualisasi interaktif',
                        [
                            ['label' => 'Home', 'url' => '/keuangan/dashboard.php', 'icon' => 'fas fa-home'],
                            ['label' => 'Laporan', 'url' => '#', 'icon' => 'fas fa-chart-line'],
                            ['label' => 'Laporan Bisnis', 'url' => '/keuangan/laporan_bisnis.php', 'icon' => 'fas fa-chart-bar']
                        ],
                        [
                            [
                                'type' => 'button',
                                'label' => 'Export Excel',
                                'icon' => 'fas fa-file-excel',
                                'class' => 'btn-success',
                                'onclick' => 'exportReport("excel")'
                            ],
                            [
                                'type' => 'button',
                                'label' => 'Export PDF',
                                'icon' => 'fas fa-file-pdf',
                                'class' => 'btn-danger',
                                'onclick' => 'exportReport("pdf")'
                            ],
                            [
                                'type' => 'button',
                                'label' => 'Print Report',
                                'icon' => 'fas fa-print',
                                'class' => 'btn-outline-primary',
                                'onclick' => 'window.print()'
                            ]
                        ],
                        [
                            'icon' => 'fas fa-chart-bar',
                            'search' => false
                        ]
                    );
                    ?>

                    <!-- Enhanced Date Filter -->
                    <?php
                    $filters = [
                        [
                            'type' => 'date',
                            'name' => 'start_date',
                            'label' => 'Tanggal Mulai',
                            'width' => '4'
                        ],
                        [
                            'type' => 'date',
                            'name' => 'end_date',
                            'label' => 'Tanggal Akhir',
                            'width' => '4'
                        ],
                        [
                            'type' => 'select',
                            'name' => 'report_type',
                            'label' => 'Jenis Analisis',
                            'width' => '4',
                            'options' => [
                                'summary' => 'Ringkasan',
                                'detailed' => 'Detail',
                                'comparison' => 'Perbandingan',
                                'trend' => 'Analisis Tren'
                            ]
                        ]
                    ];

                    renderEnhancedFilter($filters, [
                        'id' => 'businessReportFilter',
                        'method' => 'GET',
                        'collapsible' => true
                    ]);
                    ?>

                    <!-- Enhanced Business Statistics -->
                    <?php
                    $totalTransactions = ($salesSummary['total_penjualan'] ?? 0) + ($purchaseSummary['total_pembelian'] ?? 0);
                    $avgTransactionValue = $totalTransactions > 0 ? (($salesSummary['total_nilai_penjualan'] ?? 0) + ($purchaseSummary['total_nilai_pembelian'] ?? 0)) / $totalTransactions : 0;
                    $profitMarginPercent = ($salesSummary['total_nilai_penjualan'] ?? 0) > 0 ? (($grossProfit / ($salesSummary['total_nilai_penjualan'] ?? 1)) * 100) : 0;

                    $stats = [
                        [
                            'label' => 'Total Penjualan',
                            'value' => formatRupiah($salesSummary['total_nilai_penjualan'] ?? 0),
                            'icon' => 'fas fa-shopping-cart',
                            'color' => 'success',
                            'subtitle' => ($salesSummary['total_penjualan'] ?? 0) . ' transaksi penjualan',
                            'trend' => ['direction' => 'up', 'percentage' => '18.5']
                        ],
                        [
                            'label' => 'Total Pembelian',
                            'value' => formatRupiah($purchaseSummary['total_nilai_pembelian'] ?? 0),
                            'icon' => 'fas fa-shopping-basket',
                            'color' => 'danger',
                            'subtitle' => ($purchaseSummary['total_pembelian'] ?? 0) . ' transaksi pembelian',
                            'trend' => ['direction' => 'up', 'percentage' => '12.3']
                        ],
                        [
                            'label' => 'Laba Kotor',
                            'value' => formatRupiah($grossProfit),
                            'icon' => 'fas fa-chart-line',
                            'color' => $grossProfit >= 0 ? 'primary' : 'warning',
                            'subtitle' => 'Margin: ' . number_format($profitMargin, 1) . '%',
                            'progress' => min(abs($profitMarginPercent), 100),
                            'trend' => ['direction' => $grossProfit >= 0 ? 'up' : 'down', 'percentage' => abs($profitMarginPercent)]
                        ],
                        [
                            'label' => 'Rata-rata Penjualan',
                            'value' => formatRupiah($salesSummary['rata_rata_penjualan'] ?? 0),
                            'icon' => 'fas fa-calculator',
                            'color' => 'info',
                            'subtitle' => 'Per transaksi penjualan'
                        ],
                        [
                            'label' => 'ROI Bisnis',
                            'value' => number_format($profitMargin, 1) . '%',
                            'icon' => 'fas fa-percentage',
                            'color' => $profitMargin > 20 ? 'success' : ($profitMargin > 10 ? 'warning' : 'danger'),
                            'subtitle' => 'Return on Investment',
                            'progress' => min(abs($profitMargin), 100)
                        ],
                        [
                            'label' => 'Periode Analisis',
                            'value' => date('d M', strtotime($startDate)) . ' - ' . date('d M', strtotime($endDate)),
                            'icon' => 'fas fa-calendar-alt',
                            'color' => 'secondary',
                            'subtitle' => abs((strtotime($endDate) - strtotime($startDate)) / (60*60*24)) + 1 . ' hari'
                        ]
                    ];

                    renderEnhancedStatsCards($stats, [
                        'columns' => 3,
                        'style' => 'gradient',
                        'trend' => true,
                        'animate' => true
                    ]);
                    ?>

    <div class="row">
        <!-- Top Products -->
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-trophy me-2"></i>Produk Terlaris</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($topProducts)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-box fa-3x text-muted mb-3"></i>
                            <p class="text-muted mb-0">Belum ada data penjualan produk</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Produk</th>
                                        <th class="text-center">Qty</th>
                                        <th class="text-end">Total Nilai</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($topProducts as $index => $product): ?>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <span class="badge bg-primary me-2"><?= $index + 1 ?></span>
                                                <?= htmlspecialchars($product['nama_produk'] ?: 'Produk Tidak Diketahui') ?>
                                            </div>
                                        </td>
                                        <td class="text-center"><?= number_format($product['total_qty']) ?></td>
                                        <td class="text-end fw-bold"><?= formatRupiah($product['total_nilai']) ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Supplier Performance -->
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0"><i class="fas fa-truck me-2"></i>Performa Supplier</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($supplierPerformance)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-truck fa-3x text-muted mb-3"></i>
                            <p class="text-muted mb-0">Belum ada data pembelian dari supplier</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Supplier</th>
                                        <th class="text-center">Transaksi</th>
                                        <th class="text-end">Total Nilai</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($supplierPerformance as $index => $supplier): ?>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <span class="badge bg-warning me-2"><?= $index + 1 ?></span>
                                                <?= htmlspecialchars($supplier['nama_supplier'] ?: 'Supplier Tidak Diketahui') ?>
                                            </div>
                                        </td>
                                        <td class="text-center"><?= number_format($supplier['total_pembelian']) ?></td>
                                        <td class="text-end fw-bold"><?= formatRupiah($supplier['total_nilai']) ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Sales Trend Chart -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="fas fa-chart-area me-2"></i>Tren Penjualan Bulanan</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($salesTrend)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                            <p class="text-muted mb-0">Belum ada data tren penjualan</p>
                        </div>
                    <?php else: ?>
                        <canvas id="salesTrendChart" height="100"></canvas>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Sales Trend Chart
<?php if (!empty($salesTrend)): ?>
const salesTrendData = <?= json_encode($salesTrend) ?>;
const ctx = document.getElementById('salesTrendChart').getContext('2d');

new Chart(ctx, {
    type: 'line',
    data: {
        labels: salesTrendData.map(item => {
            const [year, month] = item.bulan.split('-');
            const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'Mei', 'Jun', 'Jul', 'Ags', 'Sep', 'Okt', 'Nov', 'Des'];
            return monthNames[parseInt(month) - 1] + ' ' + year;
        }),
        datasets: [{
            label: 'Total Penjualan',
            data: salesTrendData.map(item => item.total_penjualan),
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.1)',
            tension: 0.1,
            fill: true
        }, {
            label: 'Jumlah Transaksi',
            data: salesTrendData.map(item => item.jumlah_transaksi),
            borderColor: 'rgb(255, 99, 132)',
            backgroundColor: 'rgba(255, 99, 132, 0.1)',
            tension: 0.1,
            yAxisID: 'y1'
        }]
    },
    options: {
        responsive: true,
        interaction: {
            mode: 'index',
            intersect: false,
        },
        scales: {
            y: {
                type: 'linear',
                display: true,
                position: 'left',
                title: {
                    display: true,
                    text: 'Total Penjualan (Rp)'
                }
            },
            y1: {
                type: 'linear',
                display: true,
                position: 'right',
                title: {
                    display: true,
                    text: 'Jumlah Transaksi'
                },
                grid: {
                    drawOnChartArea: false,
                },
            }
        },
        plugins: {
            tooltip: {
                callbacks: {
                    label: function(context) {
                        if (context.datasetIndex === 0) {
                            return 'Total Penjualan: Rp ' + new Intl.NumberFormat('id-ID').format(context.parsed.y);
                        } else {
                            return 'Jumlah Transaksi: ' + context.parsed.y;
                        }
                    }
                }
            }
        }
    }
});
<?php endif; ?>

function exportReport(format) {
    const startDate = '<?= $startDate ?>';
    const endDate = '<?= $endDate ?>';

    // Show loading notification
    if (window.showNotification) {
        window.showNotification({
            type: 'info',
            title: 'Export Report',
            message: `Generating ${format.toUpperCase()} business report...`,
            duration: 3000
        });
    }

    if (format === 'excel') {
        // Implement Excel export
        setTimeout(() => {
            if (window.showNotification) {
                window.showNotification({
                    type: 'warning',
                    title: 'Coming Soon',
                    message: 'Excel export feature will be available soon',
                    duration: 5000
                });
            }
        }, 1000);
    } else if (format === 'pdf') {
        // Implement PDF export
        setTimeout(() => {
            if (window.showNotification) {
                window.showNotification({
                    type: 'warning',
                    title: 'Coming Soon',
                    message: 'PDF export feature will be available soon',
                    duration: 5000
                });
            }
        }, 1000);
    }
}

// Enhanced chart configuration for business analytics
document.addEventListener('DOMContentLoaded', function() {
    // Add chart theme support
    if (window.chartManager && window.Chart) {
        // Update chart colors based on current theme
        const isDark = document.documentElement.getAttribute('data-theme') === 'dark';
        Chart.defaults.color = isDark ? '#ffffff' : '#212529';
        Chart.defaults.borderColor = isDark ? '#404040' : '#dee2e6';
    }
});
</script>

<script src="assets/js/enhanced-modern.js?v=<?= time() ?>"></script>

                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/views/layouts/footer.php'; ?>
