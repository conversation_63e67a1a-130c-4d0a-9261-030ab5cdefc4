<?php
/**
 * Script untuk modernisasi semua halaman dengan icon yang tepat
 * Jalankan sekali untuk mengupdate semua halaman
 */

// Konfigurasi halaman dengan icon yang tepat
$pageConfigs = [
    'hutang.php' => [
        'title' => 'Manajemen Hutang & Piutang',
        'subtitle' => 'Kelola hutang, piutang, dan cicilan dengan sistem tracking yang komprehensif',
        'icon' => 'fas fa-credit-card',
        'breadcrumbs' => [
            ['label' => 'Home', 'url' => '/keuangan/dashboard.php', 'icon' => 'fas fa-home'],
            ['label' => 'Hutang & Piutang', 'url' => '/keuangan/hutang.php', 'icon' => 'fas fa-credit-card']
        ],
        'actions' => [
            [
                'type' => 'button',
                'label' => 'Tambah Hutang',
                'icon' => 'fas fa-plus',
                'class' => 'btn-primary',
                'data-bs-toggle' => 'modal',
                'data-bs-target' => '#addDebtModal'
            ]
        ]
    ],
    'produk.php' => [
        'title' => 'Katalog Produk & Layanan',
        'subtitle' => 'Kelola produk, layanan, dan inventory bisnis Anda dengan sistem yang terintegrasi',
        'icon' => 'fas fa-box-open',
        'breadcrumbs' => [
            ['label' => 'Home', 'url' => '/keuangan/dashboard.php', 'icon' => 'fas fa-home'],
            ['label' => 'Produk & Layanan', 'url' => '/keuangan/produk.php', 'icon' => 'fas fa-box-open']
        ],
        'actions' => [
            [
                'type' => 'button',
                'label' => 'Tambah Produk',
                'icon' => 'fas fa-plus',
                'class' => 'btn-primary',
                'data-bs-toggle' => 'modal',
                'data-bs-target' => '#addProductModal'
            ]
        ]
    ],
    'supplier.php' => [
        'title' => 'Direktori Supplier & Vendor',
        'subtitle' => 'Kelola data supplier, vendor, dan mitra bisnis dengan sistem kontak terintegrasi',
        'icon' => 'fas fa-truck-loading',
        'breadcrumbs' => [
            ['label' => 'Home', 'url' => '/keuangan/dashboard.php', 'icon' => 'fas fa-home'],
            ['label' => 'Supplier & Vendor', 'url' => '/keuangan/supplier.php', 'icon' => 'fas fa-truck-loading']
        ],
        'actions' => [
            [
                'type' => 'button',
                'label' => 'Tambah Supplier',
                'icon' => 'fas fa-plus',
                'class' => 'btn-primary',
                'data-bs-toggle' => 'modal',
                'data-bs-target' => '#addSupplierModal'
            ]
        ]
    ],
    'inventory.php' => [
        'title' => 'Manajemen Inventory & Stok',
        'subtitle' => 'Pantau stok barang, kelola inventory, dan tracking pergerakan barang secara real-time',
        'icon' => 'fas fa-warehouse',
        'breadcrumbs' => [
            ['label' => 'Home', 'url' => '/keuangan/dashboard.php', 'icon' => 'fas fa-home'],
            ['label' => 'Inventory & Stok', 'url' => '/keuangan/inventory.php', 'icon' => 'fas fa-warehouse']
        ],
        'actions' => [
            [
                'type' => 'button',
                'label' => 'Tambah Item',
                'icon' => 'fas fa-plus',
                'class' => 'btn-primary',
                'data-bs-toggle' => 'modal',
                'data-bs-target' => '#addInventoryModal'
            ]
        ]
    ],
    'return.php' => [
        'title' => 'Manajemen Return & Retur',
        'subtitle' => 'Kelola return barang, retur penjualan, dan proses pengembalian dengan tracking lengkap',
        'icon' => 'fas fa-undo-alt',
        'breadcrumbs' => [
            ['label' => 'Home', 'url' => '/keuangan/dashboard.php', 'icon' => 'fas fa-home'],
            ['label' => 'Return & Retur', 'url' => '/keuangan/return.php', 'icon' => 'fas fa-undo-alt']
        ],
        'actions' => [
            [
                'type' => 'button',
                'label' => 'Proses Return',
                'icon' => 'fas fa-plus',
                'class' => 'btn-primary',
                'data-bs-toggle' => 'modal',
                'data-bs-target' => '#addReturnModal'
            ]
        ]
    ],
    'laporan.php' => [
        'title' => 'Laporan & Analisis Keuangan',
        'subtitle' => 'Dashboard analisis keuangan dengan laporan komprehensif dan visualisasi data interaktif',
        'icon' => 'fas fa-chart-line',
        'breadcrumbs' => [
            ['label' => 'Home', 'url' => '/keuangan/dashboard.php', 'icon' => 'fas fa-home'],
            ['label' => 'Laporan & Analisis', 'url' => '/keuangan/laporan.php', 'icon' => 'fas fa-chart-line']
        ],
        'actions' => [
            [
                'type' => 'button',
                'label' => 'Generate Laporan',
                'icon' => 'fas fa-file-export',
                'class' => 'btn-primary',
                'data-bs-toggle' => 'modal',
                'data-bs-target' => '#generateReportModal'
            ]
        ]
    ]
];

function modernizePageHeader($filename, $config) {
    if (!file_exists($filename)) {
        echo "❌ File $filename tidak ditemukan.\n";
        return false;
    }
    
    $content = file_get_contents($filename);
    
    // Cari pattern header lama
    $patterns = [
        '/include \'includes\/views\/layouts\/header\.php\';.*?<div class="container-fluid[^>]*>.*?<\/div>/s',
        '/include \'includes\/views\/layouts\/header\.php\';.*?<div class="container[^>]*>.*?<\/div>/s',
        '/include \'includes\/views\/layouts\/header\.php\';.*?<!-- Page Header -->.*?<\/div>/s'
    ];
    
    // Template header baru
    $breadcrumbsJson = json_encode($config['breadcrumbs'], JSON_UNESCAPED_SLASHES);
    $actionsJson = json_encode($config['actions'], JSON_UNESCAPED_SLASHES);
    
    $newHeader = "require_once 'includes/views/layouts/modern_page_template.php';
include 'includes/views/layouts/header.php';
?>

<div class=\"content-wrapper\">
    <div class=\"container-fluid\">
        <div class=\"row justify-content-center\">
            <div class=\"col-12 col-xl-11 col-xxl-10\">
                <div class=\"main-content-container\">
                    
                    <?php
                    // Page Header
                    renderModernPageHeader(
                        '{$config['title']}',
                        '{$config['subtitle']}',
                        $breadcrumbsJson,
                        $actionsJson
                    );
                    ?>";
    
    $updated = false;
    foreach ($patterns as $pattern) {
        $newContent = preg_replace($pattern, $newHeader, $content);
        if ($newContent !== $content) {
            file_put_contents($filename, $newContent);
            echo "✅ Header $filename berhasil diupdate.\n";
            $updated = true;
            break;
        }
    }
    
    if (!$updated) {
        echo "⚠️ Pattern header tidak ditemukan di $filename.\n";
    }
    
    return $updated;
}

function addModernFooter($filename) {
    if (!file_exists($filename)) {
        return false;
    }
    
    $content = file_get_contents($filename);
    
    // Cari pattern footer lama dan ganti dengan modern footer
    $patterns = [
        '/<\/div>\s*<\/div>\s*(?=<\?php include.*footer|$)/s',
        '/<\/div>\s*(?=<\?php include.*footer|$)/s'
    ];
    
    $newFooter = "                </div>
            </div>
        </div>
    </div>
</div>";
    
    foreach ($patterns as $pattern) {
        $newContent = preg_replace($pattern, $newFooter, $content);
        if ($newContent !== $content) {
            file_put_contents($filename, $newContent);
            echo "✅ Footer $filename berhasil diupdate.\n";
            return true;
        }
    }
    
    return false;
}

function addModernCSS($filename) {
    if (!file_exists($filename)) {
        return false;
    }
    
    $content = file_get_contents($filename);
    
    // Cek apakah sudah ada modern CSS
    if (strpos($content, 'modern-pages.css') !== false) {
        echo "✅ Modern CSS sudah ada di $filename.\n";
        return true;
    }
    
    // Tambahkan modern CSS setelah header include
    $cssInclude = '<link href="assets/css/modern-pages.css?v=' . time() . '" rel="stylesheet">';
    
    // Cari posisi setelah header.php include
    $headerIncludePos = strpos($content, "include 'includes/views/layouts/header.php';");
    
    if ($headerIncludePos !== false) {
        // Insert CSS setelah header include
        $insertPos = strpos($content, '?>', $headerIncludePos) + 2;
        $newContent = substr_replace($content, "\n" . $cssInclude . "\n", $insertPos, 0);
        
        file_put_contents($filename, $newContent);
        echo "✅ Modern CSS ditambahkan ke $filename.\n";
        return true;
    }
    
    return false;
}

// Jalankan modernisasi untuk semua halaman
echo "🚀 Memulai modernisasi semua halaman dengan icon yang tepat...\n\n";

foreach ($pageConfigs as $filename => $config) {
    echo "📄 Modernizing $filename...\n";
    echo "   📝 Title: {$config['title']}\n";
    echo "   🎨 Icon: {$config['icon']}\n";
    
    // Update header dengan template modern
    modernizePageHeader($filename, $config);
    
    // Add modern CSS
    addModernCSS($filename);
    
    // Update footer
    addModernFooter($filename);
    
    echo "✅ $filename selesai dimodernisasi.\n\n";
}

echo "🎉 Semua halaman berhasil dimodernisasi dengan template modern!\n";
echo "📋 Summary:\n";
echo "   ✅ Header: Modern page header dengan breadcrumb\n";
echo "   ✅ Icons: Icon yang sesuai untuk setiap halaman\n";
echo "   ✅ Layout: Responsive layout dengan modern styling\n";
echo "   ✅ CSS: Modern CSS framework terintegrasi\n";
echo "   ✅ Footer: Consistent footer structure\n\n";
echo "📝 Catatan: Anda mungkin perlu menyesuaikan konten spesifik di setiap halaman.\n";
?>
