<?php
/**
 * Enhanced Modern Page Template
 * Comprehensive UI/UX components for all page types with responsive design
 */

// Enhanced page header with more customization options
function renderEnhancedPageHeader($title, $subtitle = '', $breadcrumbs = [], $actions = [], $options = []) {
    $showSearch = $options['search'] ?? false;
    $headerClass = $options['class'] ?? '';
    $titleIcon = $options['icon'] ?? '';
    $headerColor = $options['color'] ?? 'primary';
    ?>
    <div class="enhanced-page-header <?= $headerClass ?>">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-lg-8 col-md-7">
                    <!-- Breadcrumb Navigation -->
                    <?php if (!empty($breadcrumbs)): ?>
                    <nav aria-label="breadcrumb" class="mb-2">
                        <ol class="breadcrumb modern-breadcrumb">
                            <?php foreach ($breadcrumbs as $index => $crumb): ?>
                                <?php if ($index === count($breadcrumbs) - 1): ?>
                                    <li class="breadcrumb-item active" aria-current="page">
                                        <?php if (!empty($crumb['icon'])): ?>
                                            <i class="<?= $crumb['icon'] ?> me-1"></i>
                                        <?php endif; ?>
                                        <?= htmlspecialchars($crumb['label']) ?>
                                    </li>
                                <?php else: ?>
                                    <li class="breadcrumb-item">
                                        <a href="<?= $crumb['url'] ?>" class="breadcrumb-link">
                                            <?php if (!empty($crumb['icon'])): ?>
                                                <i class="<?= $crumb['icon'] ?> me-1"></i>
                                            <?php endif; ?>
                                            <?= htmlspecialchars($crumb['label']) ?>
                                        </a>
                                    </li>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </ol>
                    </nav>
                    <?php endif; ?>

                    <!-- Page Title -->
                    <div class="page-title-section">
                        <h1 class="page-title">
                            <?php if ($titleIcon): ?>
                                <span class="title-icon text-<?= $headerColor ?>">
                                    <i class="<?= $titleIcon ?>"></i>
                                </span>
                            <?php endif; ?>
                            <?= htmlspecialchars($title) ?>
                        </h1>
                        <?php if ($subtitle): ?>
                            <p class="page-subtitle"><?= htmlspecialchars($subtitle) ?></p>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="col-lg-4 col-md-5">
                    <div class="page-actions">
                        <?php if ($showSearch): ?>
                            <div class="search-container me-3">
                                <div class="input-group">
                                    <input type="text" class="form-control search-input" placeholder="Search...">
                                    <button class="btn btn-outline-secondary" type="button">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                        <?php endif; ?>

                        <?php if (!empty($actions)): ?>
                            <div class="action-buttons">
                                <?php foreach ($actions as $action): ?>
                                    <?php if ($action['type'] === 'button'): ?>
                                        <button type="button" 
                                                class="btn <?= $action['class'] ?? 'btn-primary' ?> action-btn"
                                                <?php if (!empty($action['onclick'])): ?>onclick="<?= $action['onclick'] ?>"<?php endif; ?>
                                                <?php if (!empty($action['data-bs-toggle'])): ?>data-bs-toggle="<?= $action['data-bs-toggle'] ?>"<?php endif; ?>
                                                <?php if (!empty($action['data-bs-target'])): ?>data-bs-target="<?= $action['data-bs-target'] ?>"<?php endif; ?>>
                                            <?php if (!empty($action['icon'])): ?>
                                                <i class="<?= $action['icon'] ?> me-2"></i>
                                            <?php endif; ?>
                                            <span class="btn-text"><?= htmlspecialchars($action['label']) ?></span>
                                        </button>
                                    <?php elseif ($action['type'] === 'link'): ?>
                                        <a href="<?= $action['url'] ?>" 
                                           class="btn <?= $action['class'] ?? 'btn-primary' ?> action-btn">
                                            <?php if (!empty($action['icon'])): ?>
                                                <i class="<?= $action['icon'] ?> me-2"></i>
                                            <?php endif; ?>
                                            <span class="btn-text"><?= htmlspecialchars($action['label']) ?></span>
                                        </a>
                                    <?php elseif ($action['type'] === 'dropdown'): ?>
                                        <div class="dropdown">
                                            <button class="btn <?= $action['class'] ?? 'btn-secondary' ?> dropdown-toggle action-btn" 
                                                    type="button" data-bs-toggle="dropdown">
                                                <?php if (!empty($action['icon'])): ?>
                                                    <i class="<?= $action['icon'] ?> me-2"></i>
                                                <?php endif; ?>
                                                <span class="btn-text"><?= htmlspecialchars($action['label']) ?></span>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <?php foreach ($action['items'] as $item): ?>
                                                    <li>
                                                        <a class="dropdown-item" href="<?= $item['url'] ?>">
                                                            <?php if (!empty($item['icon'])): ?>
                                                                <i class="<?= $item['icon'] ?> me-2"></i>
                                                            <?php endif; ?>
                                                            <?= htmlspecialchars($item['label']) ?>
                                                        </a>
                                                    </li>
                                                <?php endforeach; ?>
                                            </ul>
                                        </div>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php
}

// Enhanced stats cards with more customization
function renderEnhancedStatsCards($stats, $options = []) {
    $columns = $options['columns'] ?? 4;
    $cardStyle = $options['style'] ?? 'gradient';
    $showTrend = $options['trend'] ?? false;
    $animate = $options['animate'] ?? true;
    
    $colClass = match($columns) {
        2 => 'col-lg-6 col-md-6',
        3 => 'col-lg-4 col-md-6',
        4 => 'col-lg-3 col-md-6',
        5 => 'col-lg-2 col-md-4 col-sm-6',
        6 => 'col-lg-2 col-md-4 col-sm-6',
        default => 'col-lg-3 col-md-6'
    };
    ?>
    <div class="enhanced-stats-section mb-4">
        <div class="row g-3">
            <?php foreach ($stats as $index => $stat): ?>
                <div class="<?= $colClass ?>">
                    <div class="enhanced-stats-card <?= $cardStyle ?> <?= $animate ? 'animate-card' : '' ?>" 
                         style="animation-delay: <?= $index * 0.1 ?>s">
                        <div class="stats-card-body">
                            <div class="stats-header">
                                <div class="stats-icon-container">
                                    <div class="stats-icon bg-<?= $stat['color'] ?>">
                                        <i class="<?= $stat['icon'] ?>"></i>
                                    </div>
                                </div>
                                <?php if ($showTrend && isset($stat['trend'])): ?>
                                    <div class="stats-trend">
                                        <span class="trend-indicator trend-<?= $stat['trend']['direction'] ?>">
                                            <i class="fas fa-arrow-<?= $stat['trend']['direction'] === 'up' ? 'up' : 'down' ?>"></i>
                                            <?= $stat['trend']['percentage'] ?>%
                                        </span>
                                    </div>
                                <?php endif; ?>
                            </div>
                            
                            <div class="stats-content">
                                <h3 class="stats-value" data-value="<?= strip_tags($stat['value']) ?>">
                                    <?= $stat['value'] ?>
                                </h3>
                                <p class="stats-label"><?= htmlspecialchars($stat['label']) ?></p>
                                <?php if (!empty($stat['subtitle'])): ?>
                                    <small class="stats-subtitle"><?= htmlspecialchars($stat['subtitle']) ?></small>
                                <?php endif; ?>
                            </div>

                            <?php if (!empty($stat['progress'])): ?>
                                <div class="stats-progress">
                                    <div class="progress">
                                        <div class="progress-bar bg-<?= $stat['color'] ?>" 
                                             style="width: <?= $stat['progress'] ?>%"
                                             data-progress="<?= $stat['progress'] ?>"></div>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
    <?php
}

// Enhanced data table with advanced features
function renderEnhancedDataTable($data, $columns, $options = []) {
    $tableId = $options['id'] ?? 'dataTable';
    $searchable = $options['searchable'] ?? true;
    $sortable = $options['sortable'] ?? true;
    $pagination = $options['pagination'] ?? true;
    $responsive = $options['responsive'] ?? true;
    $striped = $options['striped'] ?? true;
    $hover = $options['hover'] ?? true;
    $actions = $options['actions'] ?? [];
    $emptyMessage = $options['empty_message'] ?? 'No data available';
    $emptyIcon = $options['empty_icon'] ?? 'fas fa-inbox';
    ?>
    <div class="enhanced-table-container">
        <?php if ($searchable): ?>
            <div class="table-controls mb-3">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="table-search">
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-search"></i>
                                </span>
                                <input type="text" class="form-control" placeholder="Search..." 
                                       onkeyup="filterTable('<?= $tableId ?>', this.value)">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="table-info text-end">
                            <small class="text-muted">
                                Showing <span id="<?= $tableId ?>_showing">0</span> of 
                                <span id="<?= $tableId ?>_total"><?= count($data) ?></span> entries
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <div class="table-responsive">
            <table class="table enhanced-table <?= $striped ? 'table-striped' : '' ?> <?= $hover ? 'table-hover' : '' ?>" 
                   id="<?= $tableId ?>">
                <thead class="table-header">
                    <tr>
                        <?php foreach ($columns as $column): ?>
                            <th class="<?= $sortable ? 'sortable' : '' ?> <?= $column['class'] ?? '' ?>"
                                <?php if ($sortable): ?>onclick="sortTable('<?= $tableId ?>', this)"<?php endif; ?>>
                                <?= htmlspecialchars($column['label']) ?>
                                <?php if ($sortable): ?>
                                    <i class="fas fa-sort sort-icon"></i>
                                <?php endif; ?>
                            </th>
                        <?php endforeach; ?>
                        <?php if (!empty($actions)): ?>
                            <th class="text-center">Actions</th>
                        <?php endif; ?>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($data)): ?>
                        <tr class="empty-row">
                            <td colspan="<?= count($columns) + (!empty($actions) ? 1 : 0) ?>" class="text-center py-5">
                                <div class="empty-state">
                                    <i class="<?= $emptyIcon ?> fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted"><?= htmlspecialchars($emptyMessage) ?></h5>
                                </div>
                            </td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($data as $row): ?>
                            <tr>
                                <?php foreach ($columns as $column): ?>
                                    <td class="<?= $column['class'] ?? '' ?>">
                                        <?php if (isset($column['render'])): ?>
                                            <?= $column['render']($row) ?>
                                        <?php else: ?>
                                            <?= htmlspecialchars($row[$column['key']] ?? '') ?>
                                        <?php endif; ?>
                                    </td>
                                <?php endforeach; ?>
                                <?php if (!empty($actions)): ?>
                                    <td class="text-center">
                                        <div class="action-buttons">
                                            <?php foreach ($actions as $action): ?>
                                                <button type="button" 
                                                        class="btn btn-sm <?= $action['class'] ?? 'btn-outline-primary' ?>"
                                                        onclick="<?= str_replace('{id}', $row['id'] ?? '', $action['onclick']) ?>"
                                                        title="<?= htmlspecialchars($action['title'] ?? $action['label']) ?>">
                                                    <i class="<?= $action['icon'] ?>"></i>
                                                </button>
                                            <?php endforeach; ?>
                                        </div>
                                    </td>
                                <?php endif; ?>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <?php if ($pagination && !empty($data)): ?>
            <div class="table-pagination mt-3">
                <nav aria-label="Table pagination">
                    <ul class="pagination justify-content-center" id="<?= $tableId ?>_pagination">
                        <!-- Pagination will be generated by JavaScript -->
                    </ul>
                </nav>
            </div>
        <?php endif; ?>
    </div>
    <?php
}

// Enhanced filter component
function renderEnhancedFilter($filters, $options = []) {
    $formId = $options['id'] ?? 'filterForm';
    $method = $options['method'] ?? 'GET';
    $action = $options['action'] ?? '';
    $collapsible = $options['collapsible'] ?? true;
    ?>
    <div class="enhanced-filter-container mb-4">
        <div class="card border-0 shadow-sm">
            <?php if ($collapsible): ?>
                <div class="card-header bg-light">
                    <h6 class="mb-0">
                        <button class="btn btn-link text-decoration-none p-0 w-100 text-start" 
                                type="button" data-bs-toggle="collapse" data-bs-target="#<?= $formId ?>Collapse">
                            <i class="fas fa-filter me-2"></i>
                            Filters
                            <i class="fas fa-chevron-down float-end mt-1"></i>
                        </button>
                    </h6>
                </div>
                <div class="collapse" id="<?= $formId ?>Collapse">
            <?php endif; ?>
            
            <div class="card-body">
                <form method="<?= $method ?>" action="<?= $action ?>" id="<?= $formId ?>" class="filter-form">
                    <div class="row g-3">
                        <?php foreach ($filters as $filter): ?>
                            <div class="col-md-<?= $filter['width'] ?? '3' ?>">
                                <label class="form-label"><?= htmlspecialchars($filter['label']) ?></label>
                                <?php if ($filter['type'] === 'select'): ?>
                                    <select name="<?= $filter['name'] ?>" class="form-select">
                                        <option value="">All</option>
                                        <?php foreach ($filter['options'] as $value => $label): ?>
                                            <option value="<?= $value ?>" 
                                                    <?= (isset($_GET[$filter['name']]) && $_GET[$filter['name']] == $value) ? 'selected' : '' ?>>
                                                <?= htmlspecialchars($label) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                <?php elseif ($filter['type'] === 'date'): ?>
                                    <input type="date" name="<?= $filter['name'] ?>" class="form-control" 
                                           value="<?= $_GET[$filter['name']] ?? '' ?>">
                                <?php elseif ($filter['type'] === 'daterange'): ?>
                                    <div class="input-group">
                                        <input type="date" name="<?= $filter['name'] ?>_start" class="form-control" 
                                               placeholder="Start date" value="<?= $_GET[$filter['name'] . '_start'] ?? '' ?>">
                                        <span class="input-group-text">to</span>
                                        <input type="date" name="<?= $filter['name'] ?>_end" class="form-control" 
                                               placeholder="End date" value="<?= $_GET[$filter['name'] . '_end'] ?? '' ?>">
                                    </div>
                                <?php else: ?>
                                    <input type="<?= $filter['type'] ?>" name="<?= $filter['name'] ?>" class="form-control" 
                                           placeholder="<?= $filter['placeholder'] ?? '' ?>" 
                                           value="<?= $_GET[$filter['name']] ?? '' ?>">
                                <?php endif; ?>
                            </div>
                        <?php endforeach; ?>
                        
                        <div class="col-md-12">
                            <div class="filter-actions">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-filter me-2"></i>Apply Filters
                                </button>
                                <a href="<?= strtok($_SERVER['REQUEST_URI'], '?') ?>" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-2"></i>Clear
                                </a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            
            <?php if ($collapsible): ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
    <?php
}
?>
