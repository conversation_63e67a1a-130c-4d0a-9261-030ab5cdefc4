<?php
/**
 * Script untuk memperbaiki footer semua halaman yang telah dimodernisasi
 */

$modernizedPages = [
    'produk.php',
    'supplier.php', 
    'inventory.php',
    'return.php',
    'laporan.php',
    'kategori.php',
    'target.php',
    'anggaran.php',
    'hutang.php'
];

function fixPageFooter($filename) {
    if (!file_exists($filename)) {
        echo "❌ File $filename tidak ditemukan.\n";
        return false;
    }
    
    $content = file_get_contents($filename);
    
    // Cari posisi sebelum <?php include footer
    $footerIncludePos = strpos($content, "<?php include 'includes/views/layouts/footer.php';");
    
    if ($footerIncludePos === false) {
        // Cari alternatif pattern
        $footerIncludePos = strpos($content, "include 'includes/views/layouts/footer.php';");
    }
    
    if ($footerIncludePos !== false) {
        // Cari posisi sebelum include footer
        $beforeFooter = substr($content, 0, $footerIncludePos);
        $afterFooter = substr($content, $footerIncludePos);
        
        // Tambahkan closing divs sebelum footer include
        $modernFooter = "
                </div>
            </div>
        </div>
    </div>
</div>

<?php ";
        
        // Gabungkan kembali
        $newContent = $beforeFooter . $modernFooter . substr($afterFooter, 6); // Skip "<?php "
        
        file_put_contents($filename, $newContent);
        echo "✅ Footer $filename berhasil diperbaiki.\n";
        return true;
    } else {
        echo "⚠️ Footer include tidak ditemukan di $filename.\n";
        return false;
    }
}

echo "🔧 Memperbaiki footer semua halaman yang telah dimodernisasi...\n\n";

foreach ($modernizedPages as $page) {
    echo "📄 Memperbaiki footer $page...\n";
    fixPageFooter($page);
}

echo "\n✅ Semua footer berhasil diperbaiki!\n";
?>
