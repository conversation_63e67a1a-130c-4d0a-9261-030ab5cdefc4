<?php
/**
 * Complete Modernization Script
 * Modernizes ALL pages in the system with responsive UI/UX
 */

// Comprehensive list of ALL pages in the system
$allPages = [
    // Dashboard & Admin
    'dashboard.php' => [
        'title' => 'Dashboard Keuangan',
        'subtitle' => 'Ringkasan lengkap aktivitas keuangan dan bisnis dengan insights real-time',
        'icon' => 'fas fa-home',
        'category' => 'dashboard'
    ],
    'admin-dashboard.php' => [
        'title' => 'Admin Dashboard',
        'subtitle' => 'Panel kontrol administrator dengan monitoring sistem komprehensif',
        'icon' => 'fas fa-tachometer-alt',
        'category' => 'admin'
    ],
    
    // Keuangan
    'transaksi.php' => [
        'title' => 'Manajemen Transaksi',
        'subtitle' => 'Kelola semua transaksi keuangan dengan sistem tracking yang terintegrasi',
        'icon' => 'fas fa-exchange-alt',
        'category' => 'keuangan'
    ],
    'kategori.php' => [
        'title' => 'Kategori Transaksi',
        'subtitle' => 'Organisir transaksi dengan sistem kategori yang fleksibel dan mudah digunakan',
        'icon' => 'fas fa-tags',
        'category' => 'keuangan'
    ],
    'target.php' => [
        'title' => 'Target Keuangan',
        'subtitle' => 'Tetapkan dan pantau pencapaian target keuangan dengan visualisasi progress',
        'icon' => 'fas fa-bullseye',
        'category' => 'keuangan'
    ],
    'anggaran.php' => [
        'title' => 'Manajemen Anggaran',
        'subtitle' => 'Rencanakan dan kontrol pengeluaran dengan sistem anggaran yang cerdas',
        'icon' => 'fas fa-calculator',
        'category' => 'keuangan'
    ],
    'investasi.php' => [
        'title' => 'Portfolio Investasi',
        'subtitle' => 'Kelola dan monitor investasi dengan analisis mendalam dan tracking ROI',
        'icon' => 'fas fa-chart-line',
        'category' => 'keuangan'
    ],
    'hutang.php' => [
        'title' => 'Hutang & Piutang',
        'subtitle' => 'Tracking hutang dan piutang dengan sistem reminder otomatis',
        'icon' => 'fas fa-credit-card',
        'category' => 'keuangan'
    ],
    
    // Bisnis
    'produk.php' => [
        'title' => 'Katalog Produk',
        'subtitle' => 'Kelola produk dengan sistem inventory terintegrasi dan tracking stok real-time',
        'icon' => 'fas fa-box-open',
        'category' => 'bisnis'
    ],
    'penjualan.php' => [
        'title' => 'Manajemen Penjualan',
        'subtitle' => 'Kelola penjualan produk dan tracking keuntungan dengan analisis mendalam',
        'icon' => 'fas fa-shopping-cart',
        'category' => 'bisnis'
    ],
    'pembelian.php' => [
        'title' => 'Manajemen Pembelian',
        'subtitle' => 'Kelola pembelian barang dan monitoring cash flow bisnis secara efisien',
        'icon' => 'fas fa-shopping-basket',
        'category' => 'bisnis'
    ],
    'supplier.php' => [
        'title' => 'Manajemen Supplier',
        'subtitle' => 'Kelola data supplier dan tracking kinerja partnership bisnis',
        'icon' => 'fas fa-truck-loading',
        'category' => 'bisnis'
    ],
    'inventory.php' => [
        'title' => 'Manajemen Inventory',
        'subtitle' => 'Kontrol stok barang dengan sistem tracking real-time dan alert otomatis',
        'icon' => 'fas fa-warehouse',
        'category' => 'bisnis'
    ],
    'return.php' => [
        'title' => 'Manajemen Return',
        'subtitle' => 'Proses return barang dengan workflow yang efisien dan tracking lengkap',
        'icon' => 'fas fa-undo-alt',
        'category' => 'bisnis'
    ],
    
    // Laporan
    'laporan.php' => [
        'title' => 'Laporan & Analisis',
        'subtitle' => 'Dashboard analisis keuangan dengan laporan komprehensif dan visualisasi interaktif',
        'icon' => 'fas fa-chart-line',
        'category' => 'laporan'
    ],
    'laporan_keuangan.php' => [
        'title' => 'Laporan Keuangan',
        'subtitle' => 'Laporan keuangan detail dengan analisis mendalam dan export multi-format',
        'icon' => 'fas fa-file-invoice-dollar',
        'category' => 'laporan'
    ],
    'laporan_bisnis.php' => [
        'title' => 'Laporan Bisnis',
        'subtitle' => 'Analisis performa bisnis dan tren penjualan dengan insights actionable',
        'icon' => 'fas fa-chart-bar',
        'category' => 'laporan'
    ],
    'laporan_pajak.php' => [
        'title' => 'Laporan Pajak',
        'subtitle' => 'Laporan pajak dan compliance dengan regulasi terbaru dan otomasi perhitungan',
        'icon' => 'fas fa-receipt',
        'category' => 'laporan'
    ],
    
    // Tools
    'kalkulator.php' => [
        'title' => 'Kalkulator Keuangan',
        'subtitle' => 'Tools kalkulator untuk berbagai perhitungan keuangan dan simulasi investasi',
        'icon' => 'fas fa-calculator',
        'category' => 'tools'
    ],
    'konverter.php' => [
        'title' => 'Konverter Mata Uang',
        'subtitle' => 'Konversi mata uang dengan kurs real-time dan historical data',
        'icon' => 'fas fa-exchange-alt',
        'category' => 'tools'
    ],
    'kalender.php' => [
        'title' => 'Kalender Keuangan',
        'subtitle' => 'Kalender dengan reminder pembayaran dan jadwal keuangan penting',
        'icon' => 'fas fa-calendar-alt',
        'category' => 'tools'
    ],
    'pengingat.php' => [
        'title' => 'Pengingat Pembayaran',
        'subtitle' => 'Sistem pengingat otomatis untuk tagihan dan pembayaran penting',
        'icon' => 'fas fa-bell',
        'category' => 'tools'
    ],
    
    // User Management
    'profile.php' => [
        'title' => 'Profil Pengguna',
        'subtitle' => 'Kelola informasi profil dan pengaturan akun dengan keamanan tinggi',
        'icon' => 'fas fa-user-circle',
        'category' => 'user'
    ],
    'users.php' => [
        'title' => 'Manajemen Pengguna',
        'subtitle' => 'Kelola pengguna sistem dan hak akses dengan role-based permissions',
        'icon' => 'fas fa-users',
        'category' => 'admin'
    ],
    
    // Admin Panel
    'settings.php' => [
        'title' => 'Pengaturan Sistem',
        'subtitle' => 'Konfigurasi sistem dan preferensi aplikasi dengan kontrol lengkap',
        'icon' => 'fas fa-cogs',
        'category' => 'admin'
    ],
    'notifications.php' => [
        'title' => 'Manajemen Notifikasi',
        'subtitle' => 'Kelola notifikasi sistem dan pengaturan alert untuk semua pengguna',
        'icon' => 'fas fa-bell-slash',
        'category' => 'admin'
    ],
    'system_customization.php' => [
        'title' => 'Kustomisasi Sistem',
        'subtitle' => 'Personalisasi tampilan dan fitur sistem sesuai kebutuhan organisasi',
        'icon' => 'fas fa-palette',
        'category' => 'admin'
    ],
    'database_management.php' => [
        'title' => 'Manajemen Database',
        'subtitle' => 'Tools administrasi database dengan backup otomatis dan monitoring performa',
        'icon' => 'fas fa-database',
        'category' => 'admin'
    ],
    'backup_restore.php' => [
        'title' => 'Backup & Restore',
        'subtitle' => 'Sistem backup otomatis dan restore data dengan scheduling fleksibel',
        'icon' => 'fas fa-download',
        'category' => 'admin'
    ],
    'logs.php' => [
        'title' => 'Log Aktivitas',
        'subtitle' => 'Monitor dan analisis log sistem untuk audit dan troubleshooting',
        'icon' => 'fas fa-list-alt',
        'category' => 'admin'
    ],
    
    // Help & Support
    'panduan.php' => [
        'title' => 'Panduan Penggunaan',
        'subtitle' => 'Panduan lengkap penggunaan sistem dengan tutorial interaktif',
        'icon' => 'fas fa-book',
        'category' => 'help'
    ],
    'faq.php' => [
        'title' => 'FAQ',
        'subtitle' => 'Pertanyaan yang sering diajukan dengan jawaban komprehensif',
        'icon' => 'fas fa-question-circle',
        'category' => 'help'
    ],
    'tutorial.php' => [
        'title' => 'Tutorial',
        'subtitle' => 'Tutorial step-by-step untuk memaksimalkan penggunaan sistem',
        'icon' => 'fas fa-graduation-cap',
        'category' => 'help'
    ],
    'support.php' => [
        'title' => 'Support Center',
        'subtitle' => 'Pusat bantuan dengan ticketing system dan live chat support',
        'icon' => 'fas fa-life-ring',
        'category' => 'help'
    ]
];

function createPageIfNotExists($filename, $config) {
    if (!file_exists($filename)) {
        echo "📝 Membuat halaman baru: $filename\n";
        
        $template = generatePageTemplate($config);
        file_put_contents($filename, $template);
        
        echo "✅ Halaman $filename berhasil dibuat.\n";
        return true;
    }
    return false;
}

function generatePageTemplate($config) {
    $breadcrumbs = generateBreadcrumbs($config['category']);
    
    return "<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';
require_once 'includes/views/layouts/enhanced_modern_template.php';

\$currentUser = getCurrentUser();
if (!\$currentUser) {
    setFlashMessage('danger', 'Sesi tidak valid. Silakan login kembali.');
    redirect('login.php');
}

\$currentPage = '" . basename($config['title']) . "';
\$pageTitle = '{$config['title']}';

// Page logic here
// TODO: Implement page functionality

include 'includes/views/layouts/header.php';
?>

<link href=\"assets/css/enhanced-modern.css?v=<?= time() ?>\" rel=\"stylesheet\">

<div class=\"content-wrapper\">
    <div class=\"container-fluid\">
        <div class=\"row justify-content-center\">
            <div class=\"col-12 col-xl-11 col-xxl-10\">
                <div class=\"main-content-container\">
                    
                    <?php
                    // Enhanced Page Header
                    renderEnhancedPageHeader(
                        '{$config['title']}',
                        '{$config['subtitle']}',
                        $breadcrumbs,
                        [
                            [
                                'type' => 'button',
                                'label' => 'Refresh',
                                'icon' => 'fas fa-sync',
                                'class' => 'btn-outline-primary',
                                'onclick' => 'location.reload()'
                            ]
                        ],
                        [
                            'icon' => '{$config['icon']}',
                            'search' => true
                        ]
                    );
                    ?>

                    <!-- Page Content -->
                    <div class=\"enhanced-content-section\">
                        <div class=\"row\">
                            <div class=\"col-12\">
                                <div class=\"card border-0 shadow-sm\">
                                    <div class=\"card-body text-center py-5\">
                                        <i class=\"{$config['icon']} fa-4x text-muted mb-3\"></i>
                                        <h4 class=\"text-muted mb-2\">{$config['title']}</h4>
                                        <p class=\"text-muted mb-4\">{$config['subtitle']}</p>
                                        <div class=\"alert alert-info\">
                                            <i class=\"fas fa-info-circle me-2\"></i>
                                            Halaman ini sedang dalam pengembangan. Fitur akan segera tersedia.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>

<script src=\"assets/js/enhanced-modern.js?v=<?= time() ?>\"></script>

<?php include 'includes/views/layouts/footer.php'; ?>
";
}

function generateBreadcrumbs($category) {
    $breadcrumbs = [
        ['label' => 'Home', 'url' => '/keuangan/dashboard.php', 'icon' => 'fas fa-home']
    ];
    
    switch($category) {
        case 'keuangan':
            $breadcrumbs[] = ['label' => 'Keuangan', 'url' => '#', 'icon' => 'fas fa-money-bill-wave'];
            break;
        case 'bisnis':
            $breadcrumbs[] = ['label' => 'Bisnis', 'url' => '#', 'icon' => 'fas fa-store'];
            break;
        case 'laporan':
            $breadcrumbs[] = ['label' => 'Laporan', 'url' => '#', 'icon' => 'fas fa-chart-line'];
            break;
        case 'tools':
            $breadcrumbs[] = ['label' => 'Tools', 'url' => '#', 'icon' => 'fas fa-tools'];
            break;
        case 'admin':
            $breadcrumbs[] = ['label' => 'Admin', 'url' => '#', 'icon' => 'fas fa-cog'];
            break;
        case 'help':
            $breadcrumbs[] = ['label' => 'Bantuan', 'url' => '#', 'icon' => 'fas fa-question-circle'];
            break;
    }
    
    return var_export($breadcrumbs, true);
}

function modernizeExistingPage($filename, $config) {
    if (!file_exists($filename)) {
        return createPageIfNotExists($filename, $config);
    }
    
    $content = file_get_contents($filename);
    $originalContent = $content;
    
    // 1. Add enhanced template include
    if (strpos($content, \"require_once 'includes/views/layouts/enhanced_modern_template.php';\") === false) {
        $content = str_replace(
            \"include 'includes/views/layouts/header.php';\",
            \"require_once 'includes/views/layouts/enhanced_modern_template.php';\\ninclude 'includes/views/layouts/header.php';\",
            $content
        );
    }
    
    // 2. Add enhanced CSS
    if (strpos($content, 'enhanced-modern.css') === false) {
        $cssInclude = '<link href=\"assets/css/enhanced-modern.css?v=' . time() . '\" rel=\"stylesheet\">';
        $headerPos = strpos($content, \"include 'includes/views/layouts/header.php';\");
        if ($headerPos !== false) {
            $insertPos = strpos($content, '?>', $headerPos) + 2;
            $content = substr_replace($content, \"\\n\" . $cssInclude . \"\\n\", $insertPos, 0);
        }
    }
    
    // 3. Wrap in enhanced structure if not already wrapped
    if (strpos($content, 'content-wrapper') === false) {
        $content = wrapInEnhancedStructure($content, $config);
    }
    
    // 4. Add enhanced JavaScript
    if (strpos($content, 'enhanced-modern.js') === false) {
        $jsInclude = '<script src=\"assets/js/enhanced-modern.js?v=' . time() . '\"></script>';
        $footerPos = strpos($content, \"include 'includes/views/layouts/footer.php';\");
        if ($footerPos !== false) {
            $content = substr_replace($content, $jsInclude . \"\\n\", $footerPos, 0);
        }
    }
    
    // 5. Fix footer structure
    $content = fixFooterStructure($content);
    
    // Only save if content changed
    if ($content !== $originalContent) {
        file_put_contents($filename, $content);
        echo \"✅ $filename berhasil dimodernisasi.\\n\";
        return true;
    } else {
        echo \"ℹ️ $filename sudah modern.\\n\";
        return false;
    }
}

function wrapInEnhancedStructure($content, $config) {
    // Find main content area
    $containerPattern = '/<div class=\"container-fluid[^>]*>/';
    $footerPattern = '/<\\?php include \'includes\\/views\\/layouts\\/footer\\.php\';/';
    
    if (preg_match($containerPattern, $content, $containerMatch, PREG_OFFSET_CAPTURE) &&
        preg_match($footerPattern, $content, $footerMatch, PREG_OFFSET_CAPTURE)) {
        
        $beforeContent = substr($content, 0, $containerMatch[0][1]);
        $mainContent = substr($content, $containerMatch[0][1], $footerMatch[0][1] - $containerMatch[0][1]);
        $afterContent = substr($content, $footerMatch[0][1]);
        
        $breadcrumbs = generateBreadcrumbs($config['category']);
        
        $enhancedHeader = \"
                    <?php
                    // Enhanced Page Header
                    renderEnhancedPageHeader(
                        '{$config['title']}',
                        '{$config['subtitle']}',
                        $breadcrumbs,
                        [
                            [
                                'type' => 'button',
                                'label' => 'Refresh',
                                'icon' => 'fas fa-sync',
                                'class' => 'btn-outline-primary',
                                'onclick' => 'location.reload()'
                            ]
                        ],
                        [
                            'icon' => '{$config['icon']}',
                            'search' => true
                        ]
                    );
                    ?>\";
        
        $wrappedContent = $beforeContent . 
            '<div class=\"content-wrapper\">
    <div class=\"container-fluid\">
        <div class=\"row justify-content-center\">
            <div class=\"col-12 col-xl-11 col-xxl-10\">
                <div class=\"main-content-container\">' . 
            $enhancedHeader .
            str_replace('<div class=\"container-fluid', '<div class=\"enhanced-content', $mainContent) .
            '
                </div>
            </div>
        </div>
    </div>
</div>

' . $afterContent;
        
        return $wrappedContent;
    }
    
    return $content;
}

function fixFooterStructure($content) {
    $footerPos = strpos($content, \"<?php include 'includes/views/layouts/footer.php';\");
    if ($footerPos !== false) {
        $beforeFooter = substr($content, 0, $footerPos);
        $afterFooter = substr($content, $footerPos);
        
        if (strpos($beforeFooter, '</div>
    </div>
</div>') === false) {
            $beforeFooter .= \"
                </div>
            </div>
        </div>
    </div>
</div>

\";
        }
        
        return $beforeFooter . $afterFooter;
    }
    
    return $content;
}

// Execute complete modernization
echo \"🚀 Memulai modernisasi SEMUA halaman sistem...\\n\\n\";

$successCount = 0;
$createdCount = 0;
$totalCount = count($allPages);

foreach ($allPages as $filename => $config) {
    echo \"📄 Memproses $filename...\\n\";
    
    if (!file_exists($filename)) {
        if (createPageIfNotExists($filename, $config)) {
            $createdCount++;
            $successCount++;
        }
    } else {
        if (modernizeExistingPage($filename, $config)) {
            $successCount++;
        }
    }
    echo \"\\n\";
}

echo \"✅ Modernisasi lengkap selesai!\\n\";
echo \"📊 Statistik:\\n\";
echo \"   📝 Halaman dibuat: $createdCount\\n\";
echo \"   🔄 Halaman dimodernisasi: $successCount\\n\";
echo \"   📄 Total halaman: $totalCount\\n\\n\";

echo \"🎉 SEMUA HALAMAN TELAH DIMODERNISASI!\\n\";
?>";
