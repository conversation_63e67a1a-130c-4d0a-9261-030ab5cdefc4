<?php
/**
 * Script untuk mengupdate semua halaman dengan template modern
 * Jalankan sekali untuk mengupdate semua halaman
 */

// Daftar halaman yang perlu diupdate
$pages = [
    'anggaran.php' => [
        'title' => 'Manajemen Anggaran',
        'subtitle' => 'Kelola anggaran bulanan dan pantau pengeluaran Anda',
        'icon' => 'fas fa-calculator',
        'breadcrumbs' => [
            ['label' => 'Home', 'url' => '/keuangan/dashboard.php', 'icon' => 'fas fa-home'],
            ['label' => 'Anggaran', 'url' => '/keuangan/anggaran.php', 'icon' => 'fas fa-calculator']
        ]
    ],
    'hutang.php' => [
        'title' => 'Manajemen Hutang',
        'subtitle' => 'Kelola hutang piutang dan cicilan dengan mudah',
        'icon' => 'fas fa-credit-card',
        'breadcrumbs' => [
            ['label' => 'Home', 'url' => '/keuangan/dashboard.php', 'icon' => 'fas fa-home'],
            ['label' => 'Hutang', 'url' => '/keuangan/hutang.php', 'icon' => 'fas fa-credit-card']
        ]
    ],
    'produk.php' => [
        'title' => 'Manajemen Produk',
        'subtitle' => 'Kelola katalog produk dan layanan bisnis Anda',
        'icon' => 'fas fa-box',
        'breadcrumbs' => [
            ['label' => 'Home', 'url' => '/keuangan/dashboard.php', 'icon' => 'fas fa-home'],
            ['label' => 'Produk', 'url' => '/keuangan/produk.php', 'icon' => 'fas fa-box']
        ]
    ],
    'supplier.php' => [
        'title' => 'Manajemen Supplier',
        'subtitle' => 'Kelola data supplier dan vendor bisnis Anda',
        'icon' => 'fas fa-truck',
        'breadcrumbs' => [
            ['label' => 'Home', 'url' => '/keuangan/dashboard.php', 'icon' => 'fas fa-home'],
            ['label' => 'Supplier', 'url' => '/keuangan/supplier.php', 'icon' => 'fas fa-truck']
        ]
    ],
    'inventory.php' => [
        'title' => 'Manajemen Inventory',
        'subtitle' => 'Pantau stok barang dan kelola inventory dengan efisien',
        'icon' => 'fas fa-warehouse',
        'breadcrumbs' => [
            ['label' => 'Home', 'url' => '/keuangan/dashboard.php', 'icon' => 'fas fa-home'],
            ['label' => 'Inventory', 'url' => '/keuangan/inventory.php', 'icon' => 'fas fa-warehouse']
        ]
    ],
    'return.php' => [
        'title' => 'Manajemen Return',
        'subtitle' => 'Kelola return barang dan pengembalian dengan mudah',
        'icon' => 'fas fa-undo',
        'breadcrumbs' => [
            ['label' => 'Home', 'url' => '/keuangan/dashboard.php', 'icon' => 'fas fa-home'],
            ['label' => 'Return', 'url' => '/keuangan/return.php', 'icon' => 'fas fa-undo']
        ]
    ],
    'laporan.php' => [
        'title' => 'Laporan Keuangan',
        'subtitle' => 'Analisis dan laporan keuangan yang komprehensif',
        'icon' => 'fas fa-chart-bar',
        'breadcrumbs' => [
            ['label' => 'Home', 'url' => '/keuangan/dashboard.php', 'icon' => 'fas fa-home'],
            ['label' => 'Laporan', 'url' => '/keuangan/laporan.php', 'icon' => 'fas fa-chart-bar']
        ]
    ]
];

function updatePageHeader($filename, $pageConfig) {
    if (!file_exists($filename)) {
        echo "File $filename tidak ditemukan.\n";
        return false;
    }
    
    $content = file_get_contents($filename);
    
    // Pattern untuk mencari header lama
    $oldHeaderPattern = '/include \'includes\/views\/layouts\/header\.php\';.*?<div class="container-fluid[^>]*>.*?<\/div>/s';
    
    // Template header baru
    $newHeader = "require_once 'includes/views/layouts/modern_page_template.php';
include 'includes/views/layouts/header.php';
?>

<div class=\"content-wrapper\">
    <div class=\"container-fluid\">
        <div class=\"row justify-content-center\">
            <div class=\"col-12 col-xl-11 col-xxl-10\">
                <div class=\"main-content-container\">
                    
                    <?php
                    // Page Header
                    renderModernPageHeader(
                        '{$pageConfig['title']}',
                        '{$pageConfig['subtitle']}',
                        " . var_export($pageConfig['breadcrumbs'], true) . ",
                        [
                            [
                                'type' => 'button',
                                'label' => 'Tambah Data',
                                'icon' => 'fas fa-plus',
                                'class' => 'btn-primary',
                                'data-bs-toggle' => 'modal',
                                'data-bs-target' => '#addModal'
                            ]
                        ]
                    );
                    ?>";
    
    // Replace header lama dengan yang baru
    $newContent = preg_replace($oldHeaderPattern, $newHeader, $content);
    
    if ($newContent !== $content) {
        file_put_contents($filename, $newContent);
        echo "✅ Header $filename berhasil diupdate.\n";
        return true;
    } else {
        echo "⚠️ Header $filename tidak berubah atau pattern tidak ditemukan.\n";
        return false;
    }
}

function addModernCSS($filename) {
    if (!file_exists($filename)) {
        return false;
    }
    
    $content = file_get_contents($filename);
    
    // Cek apakah sudah ada modern CSS
    if (strpos($content, 'modern-pages.css') !== false) {
        echo "✅ Modern CSS sudah ada di $filename.\n";
        return true;
    }
    
    // Tambahkan modern CSS setelah header include
    $cssInclude = '<link href="assets/css/modern-pages.css?v=' . time() . '" rel="stylesheet">';
    
    // Cari posisi setelah header.php include
    $headerIncludePos = strpos($content, "include 'includes/views/layouts/header.php';");
    
    if ($headerIncludePos !== false) {
        // Insert CSS setelah header include
        $insertPos = strpos($content, '?>', $headerIncludePos) + 2;
        $newContent = substr_replace($content, "\n" . $cssInclude . "\n", $insertPos, 0);
        
        file_put_contents($filename, $newContent);
        echo "✅ Modern CSS ditambahkan ke $filename.\n";
        return true;
    }
    
    return false;
}

function updatePageFooter($filename) {
    if (!file_exists($filename)) {
        return false;
    }
    
    $content = file_get_contents($filename);
    
    // Pattern untuk mencari closing div lama
    $oldFooterPattern = '/<\/div>\s*<\/div>\s*(?=<\?php include|$)/';
    
    // Template footer baru
    $newFooter = "                </div>
            </div>
        </div>
    </div>
</div>";
    
    // Replace footer lama dengan yang baru
    $newContent = preg_replace($oldFooterPattern, $newFooter, $content);
    
    if ($newContent !== $content) {
        file_put_contents($filename, $newContent);
        echo "✅ Footer $filename berhasil diupdate.\n";
        return true;
    }
    
    return false;
}

// Jalankan update untuk semua halaman
echo "🚀 Memulai update halaman dengan template modern...\n\n";

foreach ($pages as $filename => $config) {
    echo "📄 Mengupdate $filename...\n";
    
    // Update header
    updatePageHeader($filename, $config);
    
    // Add modern CSS
    addModernCSS($filename);
    
    // Update footer
    updatePageFooter($filename);
    
    echo "✅ $filename selesai diupdate.\n\n";
}

echo "🎉 Semua halaman berhasil diupdate dengan template modern!\n";
echo "📝 Catatan: Anda mungkin perlu menyesuaikan konten spesifik di setiap halaman.\n";
?>
