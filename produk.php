<?php
// Start session with secure settings
if (session_status() === PHP_SESSION_NONE) {
    session_set_cookie_params([
        'lifetime' => 86400,
        'path' => '/',
        'secure' => false, // Set to false for local development
        'httponly' => true,
        'samesite' => 'Lax' // Changed from Strict to Lax for better compatibility
    ]);
    session_start();
}

// Include required files
require_once 'config/database.php';
require_once 'includes/helpers/functions.php';
require_once 'includes/helpers/notifications.php';

// Check if user is logged in
if (!isLoggedIn()) {
    setFlashMessage('danger', 'Silakan login terlebih dahulu');
    redirect('login.php');
}

$currentUser = getCurrentUser();
if (!$currentUser) {
    session_destroy();
    setFlashMessage('danger', 'Sesi tidak valid. Silakan login kembali.');
    redirect('login.php');
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        try {
            switch ($_POST['action']) {
                case 'add':
                    // Validasi input
                    $errors = [];
                    
                    if (empty($_POST['nama_produk'])) {
                        $errors[] = 'Nama produk harus diisi';
                    } elseif (strlen($_POST['nama_produk']) < 3 || strlen($_POST['nama_produk']) > 100) {
                        $errors[] = 'Nama produk harus antara 3-100 karakter';
                    }
                    
                    if (empty($_POST['harga_beli'])) {
                        $errors[] = 'Harga beli harus diisi';
                    } elseif (!is_numeric($_POST['harga_beli']) || $_POST['harga_beli'] < 0) {
                        $errors[] = 'Harga beli tidak valid';
                    }
                    
                    if (empty($_POST['harga_jual'])) {
                        $errors[] = 'Harga jual harus diisi';
                    } elseif (!is_numeric($_POST['harga_jual']) || $_POST['harga_jual'] < 0) {
                        $errors[] = 'Harga jual tidak valid';
                    }
                    
                    if (empty($_POST['stok'])) {
                        $errors[] = 'Stok harus diisi';
                    } elseif (!is_numeric($_POST['stok']) || $_POST['stok'] < 0) {
                        $errors[] = 'Stok tidak valid';
                    }
                    
                    if (empty($errors)) {
                        // Insert produk
                        $stmt = executeQuery("
                            INSERT INTO produk (nama_produk, harga_beli, harga_jual, stok) 
                            VALUES (?, ?, ?, ?)
                        ", [
                            $_POST['nama_produk'],
                            $_POST['harga_beli'],
                            $_POST['harga_jual'],
                            $_POST['stok']
                        ]);
                        
                        if ($stmt->rowCount() > 0) {
                            // Log aktivitas
                            executeQuery("
                                INSERT INTO activity_logs (user_id, aktivitas) 
                                VALUES (?, ?)
                            ", [
                                $currentUser['id'],
                                sprintf('Menambahkan produk %s', $_POST['nama_produk'])
                            ]);
                            
                            setFlashMessage('success', 'Produk berhasil ditambahkan');
                            redirect('produk.php');
                        } else {
                            throw new Exception('Gagal menyimpan produk');
                        }
                    } else {
                        setFlashMessage('danger', implode('<br>', $errors));
                    }
                    break;

                case 'update':
                    if (empty($_POST['id'])) {
                        setFlashMessage('danger', 'ID produk tidak valid');
                        break;
                    }

                    // Validasi input
                    $errors = [];
                    
                    if (empty($_POST['nama_produk'])) {
                        $errors[] = 'Nama produk harus diisi';
                    } elseif (strlen($_POST['nama_produk']) < 3 || strlen($_POST['nama_produk']) > 100) {
                        $errors[] = 'Nama produk harus antara 3-100 karakter';
                    }
                    
                    if (empty($_POST['harga_beli'])) {
                        $errors[] = 'Harga beli harus diisi';
                    } elseif (!is_numeric($_POST['harga_beli']) || $_POST['harga_beli'] < 0) {
                        $errors[] = 'Harga beli tidak valid';
                    }
                    
                    if (empty($_POST['harga_jual'])) {
                        $errors[] = 'Harga jual harus diisi';
                    } elseif (!is_numeric($_POST['harga_jual']) || $_POST['harga_jual'] < 0) {
                        $errors[] = 'Harga jual tidak valid';
                    }
                    
                    if (empty($_POST['stok'])) {
                        $errors[] = 'Stok harus diisi';
                    } elseif (!is_numeric($_POST['stok']) || $_POST['stok'] < 0) {
                        $errors[] = 'Stok tidak valid';
                    }
                    
                    if (empty($errors)) {
                        $stmt = executeQuery("
                            UPDATE produk 
                            SET nama_produk = ?, harga_beli = ?, harga_jual = ?, stok = ?
                            WHERE id = ?
                        ", [
                            $_POST['nama_produk'],
                            $_POST['harga_beli'],
                            $_POST['harga_jual'],
                            $_POST['stok'],
                            $_POST['id']
                        ]);

                        if ($stmt->rowCount() > 0) {
                            // Log aktivitas
                            executeQuery("
                                INSERT INTO activity_logs (user_id, aktivitas) 
                                VALUES (?, ?)
                            ", [
                                $currentUser['id'],
                                sprintf('Memperbarui produk ID %s', $_POST['id'])
                            ]);
                            
                            setFlashMessage('success', 'Produk berhasil diperbarui');
                        } else {
                            setFlashMessage('danger', 'Gagal memperbarui produk');
                        }
                    } else {
                        setFlashMessage('danger', implode('<br>', $errors));
                    }
                    break;
            }
        } catch (Exception $e) {
            error_log("Product Error: " . $e->getMessage());
            setFlashMessage('danger', 'Terjadi kesalahan. Silakan coba lagi.');
        }
        redirect('produk.php');
    }
}

// Initialize variables
$whereClause = "1=1";
$params = [];

// Handle search
if (!empty($_GET['search'])) {
    $whereClause .= " AND nama_produk LIKE ?";
    $params[] = '%' . $_GET['search'] . '%';
}

// Pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$perPage = 10;
$offset = ($page - 1) * $perPage;

try {
    // Get total records
    $sql = "SELECT COUNT(*) as total FROM produk WHERE $whereClause";
    $stmt = executeQuery($sql, $params);
    $totalRecords = $stmt->fetch()['total'];
    $totalPages = ceil($totalRecords / $perPage);

    // Get products with proper error handling
    $sql = "SELECT * FROM produk WHERE $whereClause ORDER BY id ASC LIMIT ? OFFSET ?";
    $params[] = $perPage;
    $params[] = $offset;
    
    try {
        $stmt = executeQuery($sql, $params);
        $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if ($products === false) {
            throw new Exception("Gagal mengambil data produk");
        }
    } catch (PDOException $e) {
        error_log("Database Error in produk.php: " . $e->getMessage());
        throw new Exception("Gagal mengambil data produk: " . $e->getMessage());
    }
} catch (Exception $e) {
    error_log("Error in produk.php: " . $e->getMessage());
    setFlashMessage('danger', 'Terjadi kesalahan saat mengambil data produk: ' . $e->getMessage());
    $products = [];
    $totalPages = 0;
}

// Set page title
$page_title = 'Produk';

// Include header
require_once 'includes/views/layouts/modern_page_template.php';
include 'includes/views/layouts/header.php';
?>

<div class="content-wrapper">
    <div class="container-fluid">
        <div class="row justify-content-center">
            <div class="col-12 col-xl-11 col-xxl-10">
                <div class="main-content-container">

                    <?php
                    // Page Header
                    renderModernPageHeader(
                        'Katalog Produk & Layanan',
                        'Kelola produk, layanan, dan inventory bisnis Anda dengan sistem yang terintegrasi',
                        [
                            ['label' => 'Home', 'url' => '/keuangan/dashboard.php', 'icon' => 'fas fa-home'],
                            ['label' => 'Produk & Layanan', 'url' => '/keuangan/produk.php', 'icon' => 'fas fa-box-open']
                        ],
                        [
                            [
                                'type' => 'button',
                                'label' => 'Tambah Produk',
                                'icon' => 'fas fa-plus',
                                'class' => 'btn-primary',
                                'data-bs-toggle' => 'modal',
                                'data-bs-target' => '#addProductModal'
                            ]
                        ]
                    );
                    ?>

    <!-- Filter Form -->
    <div class="card border-0 shadow-sm mb-3">
        <div class="card-body py-3">
            <form action="" method="GET" class="row g-3">
                <div class="col-md-4">
                    <label class="form-label mb-1">Cari Produk</label>
                    <div class="input-group">
                        <span class="input-group-text bg-light">
                            <i class="fas fa-search text-muted"></i>
                        </span>
                        <input type="text" name="search" class="form-control" value="<?= $_GET['search'] ?? '' ?>" placeholder="Cari nama produk...">
                    </div>
                </div>
                <div class="col-12 text-end">
                    <button type="submit" class="btn btn-primary d-inline-flex align-items-center gap-2">
                        <i class="fas fa-filter"></i>
                        <span>Filter</span>
                    </button>
                    <a href="produk.php" class="btn btn-light d-inline-flex align-items-center gap-2">
                        <i class="fas fa-sync"></i>
                        <span>Reset</span>
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Products Table -->
    <div class="card border-0 shadow-sm">
        <div class="card-header bg-white py-2">
            <h5 class="card-title mb-0 d-flex align-items-center gap-2">
                <i class="fas fa-box text-primary"></i>
                <span>Daftar Produk</span>
            </h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover align-middle mb-0">
                    <thead>
                        <tr>
                            <th class="border-0 px-3">Nama Produk</th>
                            <th class="border-0 px-3">Harga Beli</th>
                            <th class="border-0 px-3">Harga Jual</th>
                            <th class="border-0 px-3">Stok</th>
                            <th class="border-0 px-3">Tanggal Dibuat</th>
                            <th class="border-0 px-3 text-center">Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($products)): ?>
                        <tr>
                            <td colspan="6" class="text-center py-4">
                                <div class="text-muted">
                                    <i class="fas fa-box fa-2x mb-2"></i>
                                    <p class="mb-0">Belum ada produk</p>
                                </div>
                            </td>
                        </tr>
                        <?php else: ?>
                        <?php foreach ($products as $p): ?>
                        <tr>
                            <td class="fw-medium px-3"><?= htmlspecialchars($p['nama_produk']) ?></td>
                            <td class="text-muted px-3"><?= formatRupiah($p['harga_beli']) ?></td>
                            <td class="text-muted px-3"><?= formatRupiah($p['harga_jual']) ?></td>
                            <td class="px-3">
                                <span class="badge bg-<?= $p['stok'] > 0 ? 'success' : 'danger' ?> rounded-pill px-3 py-1">
                                    <?= $p['stok'] ?>
                                </span>
                            </td>
                            <td class="text-muted px-3"><?= date('d/m/Y H:i', strtotime($p['created_at'])) ?></td>
                            <td class="text-center px-3">
                                <div class="btn-group">
                                    <button type="button" class="btn btn-sm btn-primary" onclick="editProduct(
                                        '<?= $p['id'] ?>',
                                        '<?= htmlspecialchars($p['nama_produk']) ?>',
                                        '<?= $p['harga_beli'] ?>',
                                        '<?= $p['harga_jual'] ?>',
                                        '<?= $p['stok'] ?>'
                                    )">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteProduct(<?= $p['id'] ?>)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if ($totalPages > 1): ?>
            <div class="px-3 py-2">
                <nav>
                    <ul class="pagination justify-content-center mb-0">
                        <?php if ($page > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?= $page - 1 ?><?= !empty($_GET) ? '&' . http_build_query($_GET) : '' ?>">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                        </li>
                        <?php endif; ?>
                        
                        <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                        <li class="page-item <?= $i === $page ? 'active' : '' ?>">
                            <a class="page-link" href="?page=<?= $i ?><?= !empty($_GET) ? '&' . http_build_query($_GET) : '' ?>">
                                <?= $i ?>
                            </a>
                        </li>
                        <?php endfor; ?>
                        
                        <?php if ($page < $totalPages): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?= $page + 1 ?><?= !empty($_GET) ? '&' . http_build_query($_GET) : '' ?>">
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                        <?php endif; ?>
                    </ul>
                </nav>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Add Product Modal -->
<div class="modal fade" id="addProductModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow">
            <div class="modal-header border-0">
                <h5 class="modal-title">Tambah Produk</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="" method="POST" class="needs-validation" novalidate>
                <div class="modal-body">
                    <input type="hidden" name="action" value="add">
                    
                    <div class="mb-3">
                        <label class="form-label">Nama Produk</label>
                        <input type="text" name="nama_produk" class="form-control" required 
                               minlength="3" maxlength="100">
                        <div class="invalid-feedback">
                            Nama produk harus diisi (3-100 karakter)
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Harga Beli</label>
                        <div class="input-group">
                            <span class="input-group-text">Rp</span>
                            <input type="number" name="harga_beli" class="form-control" required 
                                   min="0" step="100">
                        </div>
                        <div class="invalid-feedback">
                            Harga beli harus diisi (minimal 0)
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Harga Jual</label>
                        <div class="input-group">
                            <span class="input-group-text">Rp</span>
                            <input type="number" name="harga_jual" class="form-control" required 
                                   min="0" step="100">
                        </div>
                        <div class="invalid-feedback">
                            Harga jual harus diisi (minimal 0)
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Stok</label>
                        <input type="number" name="stok" class="form-control" required 
                               min="0">
                        <div class="invalid-feedback">
                            Stok harus diisi (minimal 0)
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Simpan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Product Modal -->
<div class="modal fade" id="editProductModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow">
            <div class="modal-header border-0">
                <h5 class="modal-title">Edit Produk</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="" method="POST" class="needs-validation" novalidate>
                <div class="modal-body">
                    <input type="hidden" name="action" value="update">
                    <input type="hidden" name="id" id="edit_id">
                    
                    <div class="mb-3">
                        <label class="form-label">Nama Produk</label>
                        <input type="text" name="nama_produk" id="edit_nama_produk" class="form-control" required 
                               minlength="3" maxlength="100">
                        <div class="invalid-feedback">
                            Nama produk harus diisi (3-100 karakter)
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Harga Beli</label>
                        <div class="input-group">
                            <span class="input-group-text">Rp</span>
                            <input type="number" name="harga_beli" id="edit_harga_beli" class="form-control" required 
                                   min="0" step="100">
                        </div>
                        <div class="invalid-feedback">
                            Harga beli harus diisi (minimal 0)
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Harga Jual</label>
                        <div class="input-group">
                            <span class="input-group-text">Rp</span>
                            <input type="number" name="harga_jual" id="edit_harga_jual" class="form-control" required 
                                   min="0" step="100">
                        </div>
                        <div class="invalid-feedback">
                            Harga jual harus diisi (minimal 0)
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Stok</label>
                        <input type="number" name="stok" id="edit_stok" class="form-control" required 
                               min="0">
                        <div class="invalid-feedback">
                            Stok harus diisi (minimal 0)
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Simpan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php include 'includes/views/layouts/footer.php'; ?>

<style>
.card {
    transition: transform 0.2s ease-in-out;
    background: #fff;
}

.card:hover {
    transform: translateY(-5px);
}

.btn {
    padding: 0.5rem 1rem;
    border-radius: 8px;
    transition: all 0.2s ease-in-out;
}

.btn:hover {
    transform: translateY(-2px);
}

.modal-content {
    border-radius: 15px;
}

.form-control {
    border-radius: 8px;
    padding: 0.6rem 1rem;
    border: 1px solid #e0e0e0;
}

.form-control:focus {
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.15);
    border-color: #86b7fe;
}

.form-select {
    border-radius: 8px;
    padding: 0.6rem 1rem;
    border: 1px solid #e0e0e0;
}

.input-group-text {
    border-radius: 8px 0 0 8px;
    background-color: #f8f9fa;
    border: 1px solid #e0e0e0;
}

.badge {
    font-weight: 500;
    padding: 0.5rem 1rem;
}

.bg-success {
    background-color: #198754 !important;
}

.bg-primary {
    background-color: #0d6efd !important;
}

.bg-danger {
    background-color: #dc3545 !important;
}

.btn-success {
    background-color: #198754;
    border-color: #198754;
}

.btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.btn-danger {
    background-color: #dc3545;
    border-color: #dc3545;
}

.btn-light {
    background-color: #f8f9fa;
    border-color: #e0e0e0;
}

.btn-outline-danger {
    color: #dc3545;
    border-color: #dc3545;
}

.btn-outline-danger:hover {
    background-color: #dc3545;
    color: #fff;
}

.text-muted {
    color: #6c757d !important;
}

.text-gray-800 {
    color: #212529 !important;
}

.shadow-sm {
    box-shadow: 0 .125rem .25rem rgba(0,0,0,.075) !important;
}

.shadow {
    box-shadow: 0 .5rem 1rem rgba(0,0,0,.15) !important;
}

.table > :not(caption) > * > * {
    padding: 1rem;
}

.pagination .page-link {
    border-radius: 8px;
    margin: 0 2px;
    border: none;
    color: #6c757d;
}

.pagination .page-item.active .page-link {
    background-color: #0d6efd;
    color: #fff;
}

.pagination .page-link:hover {
    background-color: #f8f9fa;
    color: #0d6efd;
}
</style>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
// Edit product
function editProduct(id, nama, hargaBeli, hargaJual, stok) {
    document.getElementById('edit_id').value = id;
    document.getElementById('edit_nama_produk').value = nama;
    document.getElementById('edit_harga_beli').value = hargaBeli;
    document.getElementById('edit_harga_jual').value = hargaJual;
    document.getElementById('edit_stok').value = stok;
    
    new bootstrap.Modal(document.getElementById('editProductModal')).show();
}

// Delete product
function deleteProduct(id) {
    Swal.fire({
        title: 'Apakah Anda yakin?',
        text: "Data produk yang dihapus tidak dapat dikembalikan!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Ya, hapus!',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.isConfirmed) {
            window.location.href = `hapus-produk.php?id=${id}`;
        }
    });
}

// Form validation
const forms = document.querySelectorAll('.needs-validation');
Array.from(forms).forEach(form => {
    form.addEventListener('submit', event => {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        form.classList.add('was-validated');
    }, false);
});
</script>

                </div>
            </div>
        </div>
    </div>
</div>