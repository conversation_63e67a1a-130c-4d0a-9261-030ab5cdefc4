/* Modern Pages CSS - Consistent UI/UX for all pages */

/* Content Layout */
.content-wrapper {
    min-height: 100vh;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    padding: 0;
}

.main-content-container {
    background: transparent;
    border-radius: 0;
    max-width: 100%;
    padding: 1.5rem;
}

/* Modern Page Header */
.modern-page-header {
    margin-bottom: 2rem;
    padding: 1.5rem 0;
}

.page-title-section .page-title {
    color: #2c3e50;
    font-weight: 700;
    font-size: 2rem;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
}

.page-title i {
    margin-right: 0.75rem;
    color: #3498db;
}

.page-subtitle {
    color: #6c757d;
    font-size: 1.1rem;
    margin-bottom: 0;
    font-weight: 400;
}

/* Modern Breadcrumb */
.modern-breadcrumb {
    background: rgba(255, 255, 255, 0.8);
    border-radius: 50px;
    padding: 0.5rem 1rem;
    margin-bottom: 1rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.modern-breadcrumb .breadcrumb-item {
    font-size: 0.9rem;
}

.breadcrumb-link {
    color: #6c757d;
    text-decoration: none;
    transition: color 0.3s ease;
}

.breadcrumb-link:hover {
    color: #3498db;
}

.modern-breadcrumb .breadcrumb-item.active {
    color: #2c3e50;
    font-weight: 600;
}

/* Page Actions */
.page-actions {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
    justify-content: flex-end;
}

.page-actions .btn {
    border-radius: 50px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.page-actions .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.2);
}

/* Modern Stats Cards */
.modern-stats-section {
    margin-bottom: 2.5rem;
}

.modern-stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 1.5rem;
    padding: 0;
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.4s ease;
    overflow: hidden;
    position: relative;
}

.modern-stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    pointer-events: none;
}

.modern-stats-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0,0,0,0.2);
}

.stats-card-1 { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.stats-card-2 { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
.stats-card-3 { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
.stats-card-4 { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }

.stats-card-body {
    padding: 2rem;
    color: white;
    position: relative;
    z-index: 2;
}

.stats-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.stats-info {
    flex: 1;
}

.stats-label {
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    opacity: 0.9;
    margin-bottom: 0.75rem;
}

.stats-value {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 0.5rem;
    line-height: 1;
}

.stats-subtitle {
    opacity: 0.8;
    font-size: 0.9rem;
}

.stats-change {
    margin-top: 0.5rem;
    font-size: 0.85rem;
    font-weight: 600;
}

.stats-change.positive { color: #2ecc71; }
.stats-change.negative { color: #e74c3c; }

.stats-icon {
    margin-left: 1rem;
}

.stats-icon .icon-wrapper {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.stats-icon i {
    font-size: 2rem;
    color: white;
}

/* Modern Cards */
.modern-card {
    border: none;
    border-radius: 1.5rem;
    box-shadow: 0 5px 25px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    overflow: hidden;
    margin-bottom: 2rem;
    background: white;
}

.modern-card:hover {
    box-shadow: 0 10px 40px rgba(0,0,0,0.12);
    transform: translateY(-2px);
}

.modern-card .card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    border-radius: 1.5rem 1.5rem 0 0;
    padding: 1.5rem 2rem;
}

.modern-card .card-title {
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0;
    font-size: 1.25rem;
}

.modern-card .card-body {
    padding: 2rem;
}

.card-actions {
    display: flex;
    gap: 0.5rem;
}

/* Modern Table */
.modern-table-container {
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: 0 5px 25px rgba(0,0,0,0.08);
    background: white;
}

.modern-table {
    margin-bottom: 0;
    border-collapse: separate;
    border-spacing: 0;
}

.modern-table .table-header th {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    font-weight: 700;
    text-transform: uppercase;
    font-size: 0.8rem;
    letter-spacing: 1px;
    padding: 1.25rem 1rem;
    border: none;
    position: relative;
}

.modern-table .table-header th:first-child {
    border-radius: 1rem 0 0 0;
}

.modern-table .table-header th:last-child {
    border-radius: 0 1rem 0 0;
}

.modern-table .table-row {
    transition: all 0.3s ease;
    border-bottom: 1px solid #f1f3f4;
}

.modern-table .table-row:hover {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    transform: scale(1.01);
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.modern-table .table-row td {
    padding: 1.25rem 1rem;
    vertical-align: middle;
    border: none;
    font-weight: 500;
}

/* Empty State */
.empty-state {
    padding: 3rem 1rem;
    text-align: center;
}

.empty-state i {
    opacity: 0.3;
    margin-bottom: 1rem;
}

.empty-state h6 {
    color: #6c757d;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

/* Modern Filter */
.modern-filter-section {
    margin-bottom: 2rem;
}

.filter-form .form-label {
    color: #2c3e50;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.filter-form .form-control,
.filter-form .form-select {
    border-radius: 0.75rem;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
    font-weight: 500;
}

.filter-form .form-control:focus,
.filter-form .form-select:focus {
    border-color: #3498db;
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
    transform: translateY(-1px);
}

.filter-form .input-group-text {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-right: none;
    border-radius: 0.75rem 0 0 0.75rem;
}

/* Buttons */
.btn {
    border-radius: 0.75rem;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    transition: all 0.3s ease;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.btn-primary {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    border: none;
}

.btn-success {
    background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
    border: none;
}

.btn-danger {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    border: none;
}

.btn-warning {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
    border: none;
}

.btn-info {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    border: none;
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-content-container {
        padding: 1rem;
    }
    
    .modern-page-header {
        margin-bottom: 1.5rem;
        padding: 1rem 0;
    }
    
    .page-title-section .page-title {
        font-size: 1.5rem;
    }
    
    .page-actions {
        justify-content: center;
        margin-top: 1rem;
    }
    
    .page-actions .btn {
        padding: 0.5rem 1rem;
        font-size: 0.8rem;
    }
    
    .stats-card-body {
        padding: 1.5rem;
    }
    
    .stats-content {
        flex-direction: column;
        text-align: center;
    }
    
    .stats-icon {
        margin-left: 0;
        margin-top: 1rem;
    }
    
    .stats-icon .icon-wrapper {
        width: 60px;
        height: 60px;
    }
    
    .stats-icon i {
        font-size: 1.5rem;
    }
    
    .stats-value {
        font-size: 2rem;
    }
    
    .modern-card .card-header,
    .modern-card .card-body {
        padding: 1.25rem;
    }
    
    .modern-table .table-row td {
        padding: 1rem 0.75rem;
        font-size: 0.9rem;
    }
}

@media (max-width: 576px) {
    .page-title-section .page-title {
        font-size: 1.25rem;
    }
    
    .stats-value {
        font-size: 1.75rem;
    }
    
    .modern-breadcrumb {
        padding: 0.4rem 0.8rem;
        font-size: 0.8rem;
    }
}

/* Animation */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modern-stats-card,
.modern-card {
    animation: fadeInUp 0.6s ease-out;
}

.modern-stats-card:nth-child(1) { animation-delay: 0.1s; }
.modern-stats-card:nth-child(2) { animation-delay: 0.2s; }
.modern-stats-card:nth-child(3) { animation-delay: 0.3s; }
.modern-stats-card:nth-child(4) { animation-delay: 0.4s; }
