<?php
// Start session with secure settings
if (session_status() === PHP_SESSION_NONE) {
    session_set_cookie_params([
        'lifetime' => 86400,
        'path' => '/',
        'secure' => false, // Set to false for local development
        'httponly' => true,
        'samesite' => 'Lax' // Changed from Strict to Lax for better compatibility
    ]);
    session_start();
}

// Include required files
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';
require_once 'includes/helpers/notifications.php';

// Check if user is logged in
if (!isLoggedIn()) {
    setFlashMessage('danger', 'Silakan login terlebih dahulu');
    redirect('login.php');
}

$currentUser = getCurrentUser();
if (!$currentUser) {
    session_destroy();
    setFlashMessage('danger', 'Sesi tidak valid. Silakan login kembali.');
    redirect('login.php');
}

$currentPage = 'kategori';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        try {
            switch ($_POST['action']) {
                case 'add':
                    // Validasi input
                    $errors = [];

                    if (empty($_POST['nama_kategori'])) {
                        $errors[] = 'Nama kategori harus diisi';
                    } elseif (strlen($_POST['nama_kategori']) < 3 || strlen($_POST['nama_kategori']) > 50) {
                        $errors[] = 'Nama kategori harus antara 3-50 karakter';
                    }

                    if (empty($_POST['tipe'])) {
                        $errors[] = 'Tipe kategori harus dipilih';
                    } elseif (!in_array($_POST['tipe'], ['pemasukan', 'pengeluaran'])) {
                        $errors[] = 'Tipe kategori tidak valid';
                    }

                    if (empty($errors)) {
                        try {
                            // Insert kategori
                            $stmt = executeQuery("
                                INSERT INTO kategori (user_id, nama_kategori, tipe, created_at)
                                VALUES (?, ?, ?, NOW())
                            ", [
                                $currentUser['id'],
                                $_POST['nama_kategori'],
                                $_POST['tipe']
                            ]);

                            if ($stmt->rowCount() > 0) {
                                // Log aktivitas
                                executeQuery("
                                    INSERT INTO aktivitas (user_id, aktivitas)
                                    VALUES (?, ?)
                                ", [
                                    $currentUser['id'],
                                    sprintf('Menambahkan kategori %s (%s)',
                                        $_POST['nama_kategori'],
                                        $_POST['tipe']
                                    )
                                ]);

                                setFlashMessage('success', 'Kategori berhasil ditambahkan');
                                redirect('kategori.php');
                            } else {
                                throw new Exception('Gagal menyimpan kategori');
                            }
                        } catch (Exception $e) {
                            error_log("Error adding category: " . $e->getMessage());
                            setFlashMessage('danger', $e->getMessage());
                        }
                    } else {
                        setFlashMessage('danger', implode('<br>', $errors));
                    }
                    break;

                case 'update':
                    if (empty($_POST['id'])) {
                        setFlashMessage('danger', 'ID kategori tidak valid');
                        break;
                    }

                    // Validasi input
                    $errors = [];

                    if (empty($_POST['nama_kategori'])) {
                        $errors[] = 'Nama kategori harus diisi';
                    } elseif (strlen($_POST['nama_kategori']) < 3 || strlen($_POST['nama_kategori']) > 50) {
                        $errors[] = 'Nama kategori harus antara 3-50 karakter';
                    }

                    if (empty($_POST['tipe'])) {
                        $errors[] = 'Tipe kategori harus dipilih';
                    } elseif (!in_array($_POST['tipe'], ['pemasukan', 'pengeluaran'])) {
                        $errors[] = 'Tipe kategori tidak valid';
                    }

                    if (empty($errors)) {
                        $stmt = executeQuery("
                            UPDATE kategori
                            SET nama_kategori = ?, tipe = ?
                            WHERE id = ? AND (user_id = ? OR user_id IS NULL)
                        ", [
                            $_POST['nama_kategori'],
                            $_POST['tipe'],
                            $_POST['id'],
                            $currentUser['id']
                        ]);

                        if ($stmt->rowCount() > 0) {
                            // Log aktivitas
                            executeQuery("
                                INSERT INTO aktivitas (user_id, aktivitas)
                                VALUES (?, ?)
                            ", [
                                $currentUser['id'],
                                sprintf('Memperbarui kategori ID %s', $_POST['id'])
                            ]);

                            setFlashMessage('success', 'Kategori berhasil diperbarui');
                        } else {
                            setFlashMessage('danger', 'Gagal memperbarui kategori');
                        }
                    } else {
                        setFlashMessage('danger', implode('<br>', $errors));
                    }
                    break;
            }
        } catch (Exception $e) {
            error_log("Category Error: " . $e->getMessage());
            setFlashMessage('danger', 'Terjadi kesalahan. Silakan coba lagi.');
        }
        redirect('kategori.php');
    }
}

// Get categories with pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$perPage = 10;
$offset = ($page - 1) * $perPage;

// Get total records with filters
$where = ["(user_id = ? OR user_id IS NULL)"];
$params = [$currentUser['id']];

if (!empty($_GET['tipe'])) {
    $where[] = "tipe = ?";
    $params[] = $_GET['tipe'];
}

if (!empty($_GET['search'])) {
    $where[] = "nama_kategori LIKE ?";
    $params[] = "%{$_GET['search']}%";
}

$whereClause = implode(" AND ", $where);

// Get total records
$stmt = executeQuery("
    SELECT COUNT(*) as total
    FROM kategori
    WHERE $whereClause
", $params);
$totalRecords = $stmt->fetch()['total'];
$totalPages = ceil($totalRecords / $perPage);

// Get categories
$stmt = executeQuery("
    SELECT * FROM kategori
    WHERE $whereClause
    ORDER BY nama_kategori ASC
    LIMIT ? OFFSET ?
", array_merge($params, [$perPage, $offset]));
$categories = $stmt->fetchAll();

// Set page title
$page_title = 'Kategori';

// Include header
require_once 'includes/views/layouts/modern_page_template.php';
include 'includes/views/layouts/header.php';
?>

<div class="content-wrapper">
    <div class="container-fluid">
        <div class="row justify-content-center">
            <div class="col-12 col-xl-11 col-xxl-10">
                <div class="main-content-container">

                    <?php
                    // Page Header
                    renderModernPageHeader(
                        'Manajemen Kategori',
                        'Kelola kategori pemasukan dan pengeluaran dengan mudah',
                        [
                            ['label' => 'Home', 'url' => '/keuangan/dashboard.php', 'icon' => 'fas fa-home'],
                            ['label' => 'Kategori', 'url' => '/keuangan/kategori.php', 'icon' => 'fas fa-tags']
                        ],
                        [
                            [
                                'type' => 'button',
                                'label' => 'Tambah Kategori',
                                'icon' => 'fas fa-plus',
                                'class' => 'btn-primary',
                                'data-bs-toggle' => 'modal',
                                'data-bs-target' => '#addCategoryModal'
                            ]
                        ]
                    );
                    ?>

                    <!-- Filter Section -->
                    <?php
                    $filters = [
                        [
                            'type' => 'text',
                            'name' => 'search',
                            'label' => 'Cari Kategori',
                            'placeholder' => 'Cari nama kategori...',
                            'icon' => 'fas fa-search',
                            'col' => '6'
                        ],
                        [
                            'type' => 'select',
                            'name' => 'tipe',
                            'label' => 'Tipe Kategori',
                            'placeholder' => 'Semua Tipe',
                            'options' => [
                                'pemasukan' => '💰 Pemasukan',
                                'pengeluaran' => '💸 Pengeluaran'
                            ],
                            'col' => '4'
                        ]
                    ];

                    renderModernFilter($filters, $_GET);
                    ?>

                    <!-- Categories Table -->
                    <?php
                    $headers = [
                        ['label' => 'Nama Kategori', 'key' => 'nama', 'class' => 'ps-4'],
                        ['label' => 'Tipe', 'key' => 'tipe', 'class' => ''],
                        ['label' => 'Tanggal Dibuat', 'key' => 'tanggal', 'class' => ''],
                        ['label' => 'Status', 'key' => 'status', 'class' => 'text-center'],
                    ];

                    $tableData = [];
                    foreach ($categories as $c) {
                        $typeColor = $c['tipe'] === 'pemasukan' ? 'success' : 'danger';
                        $typeIcon = $c['tipe'] === 'pemasukan' ? 'arrow-up' : 'arrow-down';

                        $tableData[] = [
                            'id' => $c['id'],
                            'nama' => '
                                <div class="d-flex align-items-center">
                                    <div class="category-icon bg-' . $typeColor . ' bg-opacity-15 text-' . $typeColor . ' me-3">
                                        <i class="fas fa-tag"></i>
                                    </div>
                                    <div>
                                        <div class="fw-semibold text-dark">' . htmlspecialchars($c['nama_kategori']) . '</div>
                                        <small class="text-muted">ID: #' . str_pad($c['id'], 3, '0', STR_PAD_LEFT) . '</small>
                                    </div>
                                </div>',
                            'tipe' => '
                                <span class="badge bg-' . $typeColor . ' bg-opacity-15 text-' . $typeColor . ' px-3 py-2">
                                    <i class="fas fa-' . $typeIcon . ' me-1"></i>
                                    ' . ucfirst($c['tipe']) . '
                                </span>',
                            'tanggal' => '
                                <div class="text-muted">
                                    <div>' . formatTanggal($c['created_at']) . '</div>
                                    <small>' . date('H:i', strtotime($c['created_at'])) . '</small>
                                </div>',
                            'status' => '
                                <span class="badge bg-success bg-opacity-15 text-success px-3 py-2">
                                    <i class="fas fa-check-circle me-1"></i>
                                    Aktif
                                </span>'
                        ];
                    }

                    $actions = [
                        [
                            'icon' => 'fas fa-edit',
                            'class' => 'btn-outline-primary',
                            'onclick' => 'editCategory({id})',
                            'title' => 'Edit Kategori'
                        ],
                        [
                            'icon' => 'fas fa-trash',
                            'class' => 'btn-outline-danger',
                            'onclick' => 'deleteCategory({id})',
                            'title' => 'Hapus Kategori'
                        ]
                    ];

                    renderModernCard(
                        'Daftar Kategori',
                        renderModernTable($headers, $tableData, [
                            'empty_message' => 'Belum ada kategori yang dibuat',
                            'empty_icon' => 'fas fa-tags',
                            'actions' => $actions
                        ]),
                        [
                            'icon' => 'fas fa-tags',
                            'actions' => [
                                [
                                    'type' => 'button',
                                    'label' => 'Export',
                                    'icon' => 'fas fa-download',
                                    'class' => 'btn-outline-success',
                                    'onclick' => 'exportCategories()'
                                ]
                            ]
                        ]
                    );
                    ?>

                    <!-- Pagination -->
                    <?php if ($totalPages > 1): ?>
                    <div class="d-flex justify-content-center mt-4">
                        <nav aria-label="Pagination">
                            <ul class="pagination pagination-lg">
                                <?php if ($page > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?= $page - 1 ?><?= !empty($_GET) ? '&' . http_build_query(array_filter($_GET, function($k) { return $k !== 'page'; }, ARRAY_FILTER_USE_KEY)) : '' ?>">
                                        <i class="fas fa-chevron-left"></i>
                                    </a>
                                </li>
                                <?php endif; ?>

                                <?php
                                $start = max(1, $page - 2);
                                $end = min($totalPages, $page + 2);
                                ?>

                                <?php if ($start > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=1<?= !empty($_GET) ? '&' . http_build_query(array_filter($_GET, function($k) { return $k !== 'page'; }, ARRAY_FILTER_USE_KEY)) : '' ?>">1</a>
                                </li>
                                <?php if ($start > 2): ?>
                                <li class="page-item disabled"><span class="page-link">...</span></li>
                                <?php endif; ?>
                                <?php endif; ?>

                                <?php for ($i = $start; $i <= $end; $i++): ?>
                                <li class="page-item <?= $i === $page ? 'active' : '' ?>">
                                    <a class="page-link" href="?page=<?= $i ?><?= !empty($_GET) ? '&' . http_build_query(array_filter($_GET, function($k) { return $k !== 'page'; }, ARRAY_FILTER_USE_KEY)) : '' ?>"><?= $i ?></a>
                                </li>
                                <?php endfor; ?>

                                <?php if ($end < $totalPages): ?>
                                <?php if ($end < $totalPages - 1): ?>
                                <li class="page-item disabled"><span class="page-link">...</span></li>
                                <?php endif; ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?= $totalPages ?><?= !empty($_GET) ? '&' . http_build_query(array_filter($_GET, function($k) { return $k !== 'page'; }, ARRAY_FILTER_USE_KEY)) : '' ?>"><?= $totalPages ?></a>
                                </li>
                                <?php endif; ?>

                                <?php if ($page < $totalPages): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?= $page + 1 ?><?= !empty($_GET) ? '&' . http_build_query(array_filter($_GET, function($k) { return $k !== 'page'; }, ARRAY_FILTER_USE_KEY)) : '' ?>">
                                        <i class="fas fa-chevron-right"></i>
                                    </a>
                                </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                    </div>
                    <?php endif; ?>

                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Category Modal -->
<div class="modal fade" id="addCategoryModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow">
            <div class="modal-header border-0 py-2">
                <h5 class="modal-title small fw-medium">Tambah Kategori</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="" method="POST" class="needs-validation" novalidate>
                <div class="modal-body py-2">
                    <input type="hidden" name="action" value="add">

                    <div class="mb-2">
                        <label class="form-label small mb-1">Nama Kategori</label>
                        <input type="text" name="nama_kategori" class="form-control form-control-sm" required
                               minlength="3" maxlength="50" pattern="[A-Za-z0-9\s]+">
                        <div class="invalid-feedback small">
                            Nama kategori harus diisi (3-50 karakter, hanya huruf, angka, dan spasi)
                        </div>
                    </div>

                    <div class="mb-2">
                        <label class="form-label small mb-1">Tipe</label>
                        <select name="tipe" class="form-select form-select-sm" required>
                            <option value="">Pilih Tipe</option>
                            <option value="pemasukan">Pemasukan</option>
                            <option value="pengeluaran">Pengeluaran</option>
                        </select>
                        <div class="invalid-feedback small">Tipe kategori harus dipilih</div>
                    </div>
                </div>
                <div class="modal-footer border-0 py-2">
                    <button type="button" class="btn btn-light btn-sm" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary btn-sm">Simpan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Category Modal -->
<div class="modal fade" id="editCategoryModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow">
            <div class="modal-header border-0 py-2">
                <h5 class="modal-title small fw-medium">Edit Kategori</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="" method="POST" class="needs-validation" novalidate>
                <div class="modal-body py-2">
                    <input type="hidden" name="action" value="update">
                    <input type="hidden" name="id" id="edit_id">

                    <div class="mb-2">
                        <label class="form-label small mb-1">Nama Kategori</label>
                        <input type="text" name="nama_kategori" id="edit_nama_kategori" class="form-control form-control-sm" required
                               minlength="3" maxlength="50" pattern="[A-Za-z0-9\s]+">
                        <div class="invalid-feedback small">
                            Nama kategori harus diisi (3-50 karakter, hanya huruf, angka, dan spasi)
                        </div>
                    </div>

                    <div class="mb-2">
                        <label class="form-label small mb-1">Tipe</label>
                        <select name="tipe" id="edit_tipe" class="form-select form-select-sm" required>
                            <option value="pemasukan">Pemasukan</option>
                            <option value="pengeluaran">Pengeluaran</option>
                        </select>
                        <div class="invalid-feedback small">Tipe kategori harus dipilih</div>
                    </div>
                </div>
                <div class="modal-footer border-0 py-2">
                    <button type="button" class="btn btn-light btn-sm" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary btn-sm">Simpan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php include 'includes/views/layouts/footer.php'; ?>

<style>
.card {
    transition: transform 0.2s ease-in-out;
    background: #fff;
}

.card:hover {
    transform: translateY(-5px);
}

.btn {
    padding: 0.5rem 1rem;
    border-radius: 8px;
    transition: all 0.2s ease-in-out;
}

.btn:hover {
    transform: translateY(-2px);
}

.modal-content {
    border-radius: 15px;
}

.form-control {
    border-radius: 8px;
    padding: 0.6rem 1rem;
    border: 1px solid #e0e0e0;
}

.form-control:focus {
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.15);
    border-color: #86b7fe;
}

.form-select {
    border-radius: 8px;
    padding: 0.6rem 1rem;
    border: 1px solid #e0e0e0;
}

.input-group-text {
    border-radius: 8px 0 0 8px;
    background-color: #f8f9fa;
    border: 1px solid #e0e0e0;
}

.badge {
    font-weight: 500;
    padding: 0.5rem 1rem;
}

.bg-success {
    background-color: #198754 !important;
}

.bg-primary {
    background-color: #0d6efd !important;
}

.bg-danger {
    background-color: #dc3545 !important;
}

.btn-success {
    background-color: #198754;
    border-color: #198754;
}

.btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.btn-danger {
    background-color: #dc3545;
    border-color: #dc3545;
}

.btn-light {
    background-color: #f8f9fa;
    border-color: #e0e0e0;
}

.btn-outline-danger {
    color: #dc3545;
    border-color: #dc3545;
}

.btn-outline-danger:hover {
    background-color: #dc3545;
    color: #fff;
}

.text-muted {
    color: #6c757d !important;
}

.text-gray-800 {
    color: #212529 !important;
}

.shadow-sm {
    box-shadow: 0 .125rem .25rem rgba(0,0,0,.075) !important;
}

.shadow {
    box-shadow: 0 .5rem 1rem rgba(0,0,0,.15) !important;
}

.table > :not(caption) > * > * {
    padding: 1rem;
}

.pagination .page-link {
    border-radius: 8px;
    margin: 0 2px;
    border: none;
    color: #6c757d;
}

.pagination .page-item.active .page-link {
    background-color: #0d6efd;
    color: #fff;
}

.pagination .page-link:hover {
    background-color: #f8f9fa;
    color: #0d6efd;
}

/* Category specific styles */
.category-icon {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
}

.modern-swal {
    border-radius: 1rem;
}

/* Enhanced table styles */
.modern-table .table-row:hover {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    transform: scale(1.01);
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.modern-table .table-row td {
    border: none;
    padding: 1.25rem 1rem;
    vertical-align: middle;
}

/* Responsive improvements */
@media (max-width: 768px) {
    .category-icon {
        width: 35px;
        height: 35px;
        font-size: 0.9rem;
    }

    .modern-table .table-row td {
        padding: 1rem 0.75rem;
        font-size: 0.9rem;
    }

    .badge {
        font-size: 0.75rem;
        padding: 0.4rem 0.8rem;
    }
}
</style>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
// Edit category
function editCategory(id) {
    // Get category data from the categories array
    const categories = <?= json_encode($categories) ?>;
    const category = categories.find(cat => cat.id == id);

    if (category) {
        document.getElementById('edit_id').value = id;
        document.getElementById('edit_nama_kategori').value = category.nama_kategori;
        document.getElementById('edit_tipe').value = category.tipe;

        new bootstrap.Modal(document.getElementById('editCategoryModal')).show();
    }
}

// Delete category
function deleteCategory(id) {
    Swal.fire({
        title: 'Hapus Kategori?',
        text: "Kategori yang dihapus tidak dapat dikembalikan!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Ya, Hapus!',
        cancelButtonText: 'Batal',
        customClass: {
            popup: 'modern-swal'
        }
    }).then((result) => {
        if (result.isConfirmed) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = 'kategori.php';

            const actionInput = document.createElement('input');
            actionInput.type = 'hidden';
            actionInput.name = 'action';
            actionInput.value = 'delete';

            const idInput = document.createElement('input');
            idInput.type = 'hidden';
            idInput.name = 'id';
            idInput.value = id;

            form.appendChild(actionInput);
            form.appendChild(idInput);
            document.body.appendChild(form);
            form.submit();
        }
    });
}

// Export categories
function exportCategories() {
    Swal.fire({
        title: 'Export Kategori',
        text: 'Pilih format export yang diinginkan',
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: 'Excel',
        cancelButtonText: 'PDF',
        showDenyButton: true,
        denyButtonText: 'CSV'
    }).then((result) => {
        if (result.isConfirmed) {
            window.location.href = 'kategori.php?action=export&format=excel';
        } else if (result.isDenied) {
            window.location.href = 'kategori.php?action=export&format=csv';
        } else if (result.dismiss === Swal.DismissReason.cancel) {
            window.location.href = 'kategori.php?action=export&format=pdf';
        }
    });
}

// Form validation
const forms = document.querySelectorAll('.needs-validation');
Array.from(forms).forEach(form => {
    form.addEventListener('submit', event => {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        form.classList.add('was-validated');
    }, false);
});
</script>